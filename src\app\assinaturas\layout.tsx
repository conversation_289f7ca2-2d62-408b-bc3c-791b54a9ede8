"use client";

import { Header } from "@/layout/components/header";

const SubscriptionsLayout = ({ children }: { children: React.ReactNode }) => {
	return (
		<main className="sm:min-h-screen flex- relative h-full min-h-lvh  ">
			<Header />
			<section className="bg-transparent   overflow-x-hidden flex-1 w-full min-h-[calc(100dvh-80px)] p-1 flex flex-col items-center justify-center">
				{children}
			</section>
		</main>
	);
};

export default SubscriptionsLayout;
