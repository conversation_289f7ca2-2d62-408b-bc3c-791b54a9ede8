import { cn } from "@/shared/lib/utils";
import { CheckCircle, ClipboardList, FileText, Shield, User } from "lucide-react";
import { useEffect, useRef } from "react";
import { useEpiSignatureStepper } from "../../../hooks/epi/use-epi-signature-stepper.hook";
import { StepperHeader, StepperDesktop, StepperMobile, StepContent, StepContentRenderer, StepperFooter } from "./stepper";

export interface IEpiSignatureStepperProps {
	className?: string;
}

const stepIconsProvider = [FileText, Shield];
const stepIconsUser = [ClipboardList, User, FileText, CheckCircle];

export const EpiSignatureStepper = ({ className }: IEpiSignatureStepperProps) => {
	const stepContentRef = useRef<HTMLDivElement>(null);

	const {
		currentStep,
		totalSteps,
		isLoading,
		canProceed,
		isFirstStep,
		isLastStep,
		progress,
		stepConfig,
		userRole,
		handleNextStep,
		handlePreviousStep,
		handleStepChange,
		handleReset,
		handleReturnToProvider,
		getStepStatus,
		getCurrentStepConfig,
	} = useEpiSignatureStepper();

	useEffect(() => {
		if (stepContentRef.current) {
			const yOffset = -60;
			const y = stepContentRef.current.getBoundingClientRect().top + window.pageYOffset + yOffset;
			window.scrollTo({ top: y, behavior: "instant" });
		}
	}, [currentStep]);
	const renderStepContent = () => {
		return <StepContentRenderer currentStep={currentStep} userRole={userRole} onReturnToProvider={handleReturnToProvider} />;
	};

	const getStepIcon = (stepId: number) => {
		if (userRole === "provider") return stepIconsProvider[stepId - 1] || FileText;
		return stepIconsUser[stepId - 1] || ClipboardList;
	};

	return (
		<div className={cn("w-full space-y-2 md:space-y-6 max-w-6xl mx-auto", className)}>
			<StepperHeader currentStep={currentStep} totalSteps={totalSteps} progress={progress} getCurrentStepConfig={getCurrentStepConfig} />
			<StepperDesktop stepConfig={stepConfig} currentStep={currentStep} getStepStatus={getStepStatus} handleStepChange={handleStepChange} />
			<StepperMobile
				stepConfig={stepConfig}
				currentStep={currentStep}
				progress={progress}
				getCurrentStepConfig={getCurrentStepConfig}
				getStepStatus={getStepStatus}
				handleStepChange={handleStepChange}
				getStepIcon={getStepIcon}
			/>
			<StepContent>{renderStepContent()}</StepContent>
			<StepperFooter
				handlePreviousStep={handlePreviousStep}
				handleNextStep={handleNextStep}
				handleReset={handleReset}
				isLoading={isLoading}
				isFirstStep={isFirstStep}
				isLastStep={isLastStep}
				currentStep={currentStep}
				totalSteps={totalSteps}
				progress={progress}
				canProceed={canProceed}
				userRole={userRole}
			/>
		</div>
	);
};
