import { getNavItems } from "@/layout/hooks/header/nav-itens.hook";
import { useResetAllAtoms } from "@/modules/signature/hooks/signature/sign-page/reset-all-atoms.hook";
import { Sheet, SheetClose, Sheet<PERSON>ontent, Sheet<PERSON>eader, She<PERSON><PERSON>itle, SheetTrigger } from "@/shared/components/ui/sheet";
import { Menu } from "lucide-react";
import Link from "next/link";

export const MobileNav = () => {
	const navItems = getNavItems();
	const { resetAllAtoms } = useResetAllAtoms();

	return (
		<div className="lg:hidden">
			<Sheet>
				<SheetTrigger>
					<Menu className="w-6 h-6 text-gray-800" />
				</SheetTrigger>
				<SheetContent className="fixed top-0 h-full w-2/3 bg-white p-6 shadow-lg transition-transform duration-300 ease-in-out">
					<SheetHeader>
						<SheetTitle className="text-xl font-bold text-gray-900">Menu</SheetTitle>
					</SheetHeader>
					<nav className="mt-10">
						<ul className="space-y-6">
							{navItems.map(item => (
								<li key={item.href}>
									<SheetClose asChild>
										<Link
											onClick={resetAllAtoms}
											href={item.href}
											className="flex items-center px-4 py-3 rounded-lg bg-gray-100 hover:bg-blue-100 text-gray-800 transition-colors duration-200 shadow-sm"
										>
											{item.Icon && <item.Icon className="w-8 h-8 mr-2" />}
											<span className="font-medium text-center">{item.label}</span>
										</Link>
									</SheetClose>
								</li>
							))}
						</ul>
					</nav>
				</SheetContent>
			</Sheet>
		</div>
	);
};
