export interface IPdfOverlay {
	id: string;
	page: number;
	x: number;
	y: number;
	scale: number;
	type: string;
}

export interface IPdfOverlayRenderer {
	supportedType: string;
	render(context: CanvasRenderingContext2D, overlay: IPdfOverlay, scale: number, data?: unknown): Promise<void>;
}

export interface IPdfOverlayManager {
	addOverlay(overlay: IPdfOverlay): void;
	removeOverlay(id: string): void;
	getOverlaysForPage(page: number): IPdfOverlay[];
	registerRenderer(renderer: IPdfOverlayRenderer): void;
	unregisterRenderer(type: string): void;
}

export interface IPdfWithOverlaysProps {
	overlays?: IPdfOverlay[];
	overlayData?: Record<string, unknown>;
	onOverlayClick?: (overlay: IPdfOverlay) => void;
}
