import { IResponseDocumentSigned, IResponseError } from "@/shared/types/requests";
import axios, { AxiosError } from "axios";

export const handleGetDocumentError = (error: unknown): IResponseError | IResponseDocumentSigned => {
	if (error instanceof AxiosError) {
		if (error.config?.baseURL) {
			const url = "/documento/buscar?token=";

			if (error.config.url?.startsWith(url) && error.response?.status === 400) {
				if (error.response.data?.message === "Assinatura já realizada") {
					return {
						success: true,
						data: error.response.data,
						status: 400,
					};
				}
			}
		} else {
			return handleGlobalErrors(error);
		}
	}
	return handleGlobalErrors(error);
};

export const handleGlobalErrors = (error: unknown): IResponseError  => {
	if (error instanceof axios.Cancel) {
		return {
			success: false,
			data: {
				message: error.message ?? "O cookie passado não é valido no sistema",
			},
			status: 400,
		};
	}

	if (error instanceof AxiosError) {
		

		if (error.response) {
			const { status, data, config } = error.response;
			if (typeof data === "string" && data.startsWith("<!DOCTYPE html>")) {
				return {
					success: false,
					data: {
						message: "Ocorreu um erro de servidor",
						method: config.method,
						url: config.url,
					},
					status,
				};
			}
			const message = data?.message || (typeof data === "string" ? data : "Ocorreu um erro desconhecido");
			return {
				success: false,
				data: {
					message,
					method: config.method,
					url: config.url,
				},
				status,
			};
		}
		if (error.request) {
			const { url, method } = error.request as { url: string; method: string };
			return {
				success: false,
				data: {
					message: "O servidor não respondeu",
					method,
					url,
				},
				status: 0,
			};
		}
	} else if (error instanceof Error) {
		return {
			success: false,
			data: {
				message: error.message,
			},
			status: 0,
		};
	}
	return {
		success: false,
		data: {
			message: "Ocorreu um erro desconhecido",
		},
		status: 0,
	};
};
