"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { TERM_ENDPOINTS } from "../../endpoints";

export const getTermFile = async (idTermo: string | number): Promise<ApiResponseReturn<string>> => {
	const response = await createRequestAdmin<ArrayBuffer | Buffer | string | number[]>({
		method: "GET",
		path: TERM_ENDPOINTS.GET_FILE(idTermo),
		responseType: "arraybuffer",
	});

	if (!response.success) {
		return response;
	}

	let buffer: ArrayBuffer;

	if (Buffer.isBuffer(response.data)) {
		const bufferString = response.data.toString("utf8", 0, 10);
		if (bufferString.startsWith("%PDF")) {
			const nodeBuffer = response.data.buffer;
			if (nodeBuffer instanceof ArrayBuffer) {
				buffer = nodeBuffer.slice(response.data.byteOffset, response.data.byteOffset + response.data.byteLength);
			} else {
				buffer = new Uint8Array(response.data).buffer;
			}
		} else {
			const fullBufferString = response.data.toString("utf8");
			try {
				const bytesArray = JSON.parse(fullBufferString);
				if (Array.isArray(bytesArray)) {
					buffer = new Uint8Array(bytesArray).buffer;
				} else {
					throw new Error("Formato de dados inválido");
				}
			} catch {
				return {
					success: false,
					data: { message: "Formato de dados inválido retornado pelo servidor" },
					status: 500,
				};
			}
		}
	} else if (typeof response.data === "string") {
		try {
			const bytesArray = JSON.parse(response.data);
			if (Array.isArray(bytesArray)) {
				buffer = new Uint8Array(bytesArray).buffer;
			} else {
				throw new Error("Formato de dados inválido");
			}
		} catch {
			return {
				success: false,
				data: { message: "Formato de dados inválido retornado pelo servidor" },
				status: 500,
			};
		}
	} else if (Array.isArray(response.data)) {
		buffer = new Uint8Array(response.data).buffer;
	} else if (response.data instanceof ArrayBuffer) {
		buffer = response.data;
	} else {
		return {
			success: false,
			data: { message: "Tipo de dados não suportado retornado pelo servidor" },
			status: 500,
		};
	}

	const uint8Array = new Uint8Array(buffer);
	const base64 = Buffer.from(uint8Array).toString("base64");

	return {
		success: true,
		data: base64,
		status: response.status,
	};
};
