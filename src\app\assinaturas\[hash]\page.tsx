"use client";
import React from "react";

import { ErrorGetDocument } from "@/modules/signature/components/document-sign-page/pages/error-page";
import { Loading } from "@/modules/signature/components/document-sign-page/pages/loading";
import { DocumentStatus } from "@/modules/signature/components/document-sign-page/pages/verify-status/document-status";
import useDocumentHash from "@/modules/signature/hooks/document/document-hash.hook";
import { useGetDocument } from "@/modules/signature/hooks/document/get-document.hook";
import { SubscriptionPageProps } from "@/modules/signature/types/pages/sign-page.type";

const SubscriptionPage: React.FC<SubscriptionPageProps> = ({ params }) => {
	const { hash } = React.use(params);
	useDocumentHash(hash);
	const { isLoading, documentData, error, isFetching } = useGetDocument();

	const renderContent = () => {
		if (error) return <ErrorGetDocument error={error} />;
		if (isLoading || isFetching) return <Loading />;
		if (documentData?.isDocumentSigned === undefined) return <Loading />;
		return <DocumentStatus isDocumentSigned={documentData.isDocumentSigned} documentData={documentData} />;
	};

	return renderContent();
};

export default SubscriptionPage;
