import { CalendarIcon } from "lucide-react";

interface DateCellProps {
	dateString: string;
	formatDate: (dateString: string) => string;
}

export function DateCell({ dateString, formatDate }: DateCellProps) {
	return (
		<div className="flex items-center gap-2 text-gray-600">
			<CalendarIcon size={14} className="text-gray-400" />
			<div>
				<p className="text-sm font-medium">{formatDate(dateString)}</p>
			</div>
		</div>
	);
}
