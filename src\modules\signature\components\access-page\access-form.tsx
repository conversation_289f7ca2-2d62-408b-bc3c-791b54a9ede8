"use client";

import { <PERSON><PERSON> } from "@/shared/components/ui/button";

import { Input } from "@/shared/components/ui/input";
import { File } from "lucide-react";
import { useAccessForm } from "../../hooks/access/access-form.hook";
import { useHandleAccess } from "../../hooks/access/handle-access.hook";
import { TitleCard } from "../cards/card-title";
import { SubTitleCard } from "../cards/subtitle";

export const AccessForm = () => {
	const methods = useAccessForm();
	const { onAccessDocument } = useHandleAccess();
	return (
		<form onSubmit={methods.handleSubmit(onAccessDocument)}>
			<header className="text-center w-full mb-2">
				<TitleCard title={"Acessar Documento"} />
				<SubTitleCard
					title={"Para acessar o documento, por favor, insira o código de acesso enviado para o seu e-mail ou clique no link recebido."}
				/>
			</header>
			<div className="mb-8 w-full">
				<Input
					id="documentHash"
					{...methods.register("documentHash", { required: true })}
					placeholder="Digite o código de acesso aqui"
					className="w-full  h-[60px] px-4 py-3 text-xl border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pormade "
				/>
				{methods.formState.errors.documentHash && (
					<span className="text-red-500">{String(methods.formState.errors.documentHash?.message)}</span>
				)}
			</div>
			<Button
				disabled={!methods.watch("documentHash")}
				type="submit"
				className="w-full  h-[60px] font-bold text-white  bg-gray-800 hover:bg-gray-900 flex items-center justify-center gap-2 py-3"
			>
				<File />
				Acessar Documento
			</Button>

			<p className="text-xs text-gray-500 mt-4 text-center">
				* Para garantir a segurança do documento, é necessário inserir o código de acesso correto ou acessar pelo link fornecido no email.
			</p>
		</form>
	);
};
