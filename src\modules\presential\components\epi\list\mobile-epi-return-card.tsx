"use client";
import { IEpiReturn } from "@/modules/presential/services/requests/epi/find-all-return";
import { Button } from "@/shared/components/ui/button";
import { Separator } from "@/shared/components/ui/separator";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { Calendar, ChevronDown, Eye, HardHat, Shield } from "lucide-react";
import { useState } from "react";

interface MobileEpiReturnCardProps {
	epiReturn: IEpiReturn;
	getTermName: (id: number) => string | undefined;
	formatDate: (dateString: string) => string;
	// getStatusDisplay: (isSigned: boolean) => JSX.Element;
	isTermsLoading: boolean;
}

export const MobileEpiReturnCard: React.FC<MobileEpiReturnCardProps> = ({ epiReturn, getTermName, formatDate, isTermsLoading }) => {
	const [isExpanded, setIsExpanded] = useState(false);

	const handleToggleExpand = () => {
		setIsExpanded(prev => !prev);
	};

	const termName = getTermName(epiReturn.idTerm);
	const shortTermName = termName && termName.length > 30 ? termName.substring(0, 27) + "..." : termName;

	return (
		<div className="bg-white rounded-lg shadow border border-gray-100 mb-3 overflow-hidden">
			<div
				className="p-3 cursor-pointer transition-colors hover:bg-gray-50"
				onClick={handleToggleExpand}
				onKeyDown={e => {
					if (e.key === "Enter" || e.key === " ") {
						e.preventDefault();
						handleToggleExpand();
					}
				}}
				tabIndex={0}
				role="button"
				aria-expanded={isExpanded}
				aria-label="Expandir para ver mais detalhes da devolução de EPI"
			>
				<div className="flex items-center justify-between mb-2">
					<div className="flex items-center gap-2 flex-1 min-w-0">
						<div className="p-2 rounded-md bg-blue-100 text-blue-600 flex-shrink-0">
							<Shield size={18} />
						</div>
						<div className="flex-1 min-w-0">
							<h3
								className="font-medium text-gray-900 truncate"
								title={`ID: ${epiReturn.id} | Grupo EPI #${epiReturn.idWithdrawalEpiGroup}`}
							>
								ID #{epiReturn.id} • Grupo #{epiReturn.idWithdrawalEpiGroup}
							</h3>
							{isTermsLoading ? (
								<Skeleton className="h-4 w-32 mt-1" />
							) : (
								<p className="text-sm text-gray-600 truncate" title={termName}>
									{shortTermName}
								</p>
							)}
						</div>
					</div>
					<ChevronDown size={18} className={`text-gray-400 transition-transform duration-300 ${isExpanded ? "rotate-180" : ""}`} />
				</div>

				<div className="flex items-center justify-between">
					<div className="flex items-center gap-1 text-xs text-gray-500">
						<Calendar size={12} className="text-gray-400" />
						<span>{formatDate(epiReturn.signatureDate)}</span>
					</div>

					<div className="flex items-center gap-2">
						<span className="text-xs text-gray-500">Pessoa #{epiReturn.idPerson}</span>
						{/* {getStatusDisplay(epiReturn.isSigned)} */}
					</div>
				</div>
			</div>
			{isExpanded && (
				<>
					<Separator />
					<div className="p-3 bg-gray-50">
						<div className="space-y-2 mb-3">
							<div className="flex items-center gap-2 text-sm">
								<HardHat size={14} className="text-gray-400" />
								<span className="text-gray-600">ID do Grupo:</span>
								<span className="font-medium">#{epiReturn.idWithdrawalEpiGroup}</span>
							</div>
							<div className="flex items-center gap-2 text-sm">
								<Shield size={14} className="text-gray-400" />
								<span className="text-gray-600">Chave de Assinatura:</span>
								<span className="font-mono text-xs bg-gray-200 px-2 py-1 rounded">{epiReturn.idSignatureKey}</span>
							</div>
							<div className="flex items-center gap-2 text-sm">
								<Calendar size={14} className="text-gray-400" />
								<span className="text-gray-600">Pessoa:</span>
								<span className="font-medium">#{epiReturn.idPerson}</span>
							</div>
						</div>
						<div className="flex flex-col sm:flex-row gap-2">
							<Button
								variant="outline"
								size="sm"
								className="flex-1 h-8 p-2 border-gray-200 bg-white hover:bg-gray-50 hover:text-blue-600 text-gray-700"
								onClick={e => {
									e.stopPropagation();
									console.log("Visualizar devolução EPI:", epiReturn.id);
								}}
							>
								<Eye size={14} className="mr-2" /> Visualizar
							</Button>
						</div>
					</div>
				</>
			)}
		</div>
	);
};
