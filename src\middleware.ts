import { NextRequest } from "next/server";
import { adminInterceptor } from "./layout/middlewares/admin-interceptor";
import { routeInterceptor } from "./layout/middlewares/route-interceptor.mod";

export function middleware(request: NextRequest) {
	const adminResponse = adminInterceptor(request);
	if (adminResponse) return adminResponse;

	return routeInterceptor(request);
}

export const config = {
	matcher: ["/((?!api|_next|static|favicon.ico).*)"],
};
