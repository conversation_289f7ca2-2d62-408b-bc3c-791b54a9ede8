import React from "react";
import { IStepConfig } from "./stepper-header";
import { StepItem } from "./step-item";

interface IStepperDesktopProps {
	stepConfig: IStepConfig[];
	currentStep: number;
	getStepStatus: (stepId: number) => "active" | "completed" | "pending";
	handleStepChange: (stepId: number) => void;
}

export const StepperDesktop = ({ stepConfig, currentStep, getStepStatus, handleStepChange }: IStepperDesktopProps) => {
	return (
		<div className="hidden md:block p-3 md:p-6 bg-white shadow-lg rounded-lg">
			<div className="flex items-center justify-between gap-2 lg:gap-4">
				{stepConfig.map((step, index) => {
					const status = getStepStatus(step.id);
					const isClickable = step.id <= currentStep || status === "completed";
					const isLast = index === stepConfig.length - 1;

					return (
						<StepItem
							key={step.id}
							step={step}
							status={status}
							isLast={isLast}
							isClickable={isClickable}
							currentStep={currentStep}
							variant="desktop"
							onStepClick={handleStepChange}
						/>
					);
				})}
			</div>
		</div>
	);
};
