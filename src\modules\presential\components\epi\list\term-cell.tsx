import { Skeleton } from "@/shared/components/ui/skeleton";

interface TermCellProps {
	termId: number;
	getTermName: (id: number) => string;
	isLoading: boolean;
}

export function TermCell({ termId, getTermName, isLoading }: TermCellProps) {
	if (isLoading) {
		return (
			<div className="flex items-center gap-3">
				<div className="space-y-1">
					<Skeleton className="h-4 w-32" />
					<Skeleton className="h-3 w-20" />
				</div>
			</div>
		);
	}

	const termName = getTermName(termId);

	return (
		<div className="flex items-center gap-3">
			<div>
				<p className="font-medium text-gray-900 truncate max-w-[160px]" title={termName}>
					{termName}
				</p>
			</div>
		</div>
	);
}
