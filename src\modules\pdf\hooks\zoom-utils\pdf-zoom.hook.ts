import { UseZoomControlProps } from "@/modules/pdf/types/pdf-navigation.type";
import { useCallback, useState } from "react";

export function useZoomControl({ initialZoom = 1, maxZoom = Infinity }: UseZoomControlProps) {
	const [zoom, setZoom] = useState<number>(initialZoom);

	const increaseZoom = useCallback(() => {
		setZoom(z => Math.min(z + 0.2, maxZoom));
	}, [maxZoom]);

	const decreaseZoom = useCallback(() => setZoom(z => Math.max(z - 0.2, 0.2)), []);

	return { zoom, increaseZoom, decreaseZoom, setZoom };
}
