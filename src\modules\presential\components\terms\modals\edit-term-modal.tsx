import { TermModal } from "./term-modal";

interface EditTermSheetProps {
	isOpen: boolean;
	onOpenChange: (open: boolean) => void;
	selectedTermId: number | null;
	initialData: {
		title: string;
		idTermTag: string;
	};
	onClose: () => void;
}

export const EditTermSheet = ({ isOpen, onOpenChange, selectedTermId, onClose, initialData }: EditTermSheetProps) => {
	return (
		<TermModal
			isOpen={isOpen}
			onOpenChange={onOpenChange}
			mode="edit"
			initialData={initialData}
			selectedTermId={selectedTermId}
			onClose={onClose}
		/>
	);
};
