import { ErrorData } from "@/shared/types/requests";
import { IDocumentAlreadySigned, IDocumentData, IGetDocument } from "../document/get-document.type";

export interface Params {
	hash: string;
}

export interface SubscriptionPageProps {
	params: Promise<Params>;
}

export interface DocumentStatusProps {
	isDocumentSigned: boolean;
	documentData: IDocumentData<IGetDocument | IDocumentAlreadySigned | ErrorData> | undefined;
}
