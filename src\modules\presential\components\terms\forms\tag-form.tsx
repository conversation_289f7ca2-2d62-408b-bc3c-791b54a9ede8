import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { AlertCircle, Tag } from "lucide-react";
import { Control, Controller, FieldErrors } from "react-hook-form";

interface TagFormProps {
	control: Control<{ name: string }>;
	errors: FieldErrors<{ name: string }>;
}

export const TagForm = ({ control, errors }: TagFormProps) => {
	return (
		<div className="grid gap-6 py-4">
			<div className="grid gap-2">
				<div className="flex items-center gap-2">
					<Tag className="h-5 w-5 text-gray-600" />
					<Label htmlFor="tag-name" className="font-medium text-gray-700">
						Nome da Tag
					</Label>
				</div>
				<Controller
					name="name"
					control={control}
					render={({ field }) => (
						<Input
							id="tag-name"
							{...field}
							className="rounded-lg border-gray-300 focus:ring-2 focus:ring-pormade focus:border-pormade transition-colors"
							placeholder="Digite o nome da tag"
						/>
					)}
				/>
				{errors.name && (
					<div className="flex items-center gap-1.5 text-amber-600 text-sm mt-1">
						<AlertCircle className="h-4 w-4" />
						<span>{errors.name.message as string}</span>
					</div>
				)}
			</div>
		</div>
	);
};
