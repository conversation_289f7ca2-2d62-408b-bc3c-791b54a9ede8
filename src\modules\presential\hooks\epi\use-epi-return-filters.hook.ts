import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

export const epiReturnFiltersSchema = z.object({
	personId: z.string().optional(),

	signed: z.enum(["all", "true", "false"]).optional(),
});

export type EpiReturnFiltersType = z.infer<typeof epiReturnFiltersSchema>;

export const useEpiReturnFilters = () => {
	const form = useForm<EpiReturnFiltersType>({
		resolver: zodResolver(epiReturnFiltersSchema),
		defaultValues: {
			personId: "",
			signed: "all",
		},
	});

	const getFilterParams = (data: EpiReturnFiltersType) => {
		return {
			personId: data.personId ? Number(data.personId) : undefined,
			signed: data.signed === "all" ? undefined : data.signed === "true",
		};
	};

	const hasActiveFilters = () => {
		const values = form.getValues();
		return !!(values.personId || (values.signed && values.signed !== "all"));
	};

	return {
		form,
		getFilterParams,
		hasActiveFilters,
	};
};
