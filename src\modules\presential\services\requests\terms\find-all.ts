"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { TERM_ENDPOINTS } from "../../endpoints";

export interface ITerm {
	id: number;
	createDate: string;
	title: string;
	fileName: string;
	idTermTag: number;
}

export const findAllTerms = async (): Promise<ApiResponseReturn<ITerm[]>> => {
	return createRequestAdmin({
		method: "GET",
		path: TERM_ENDPOINTS.GET_ALL,
	});
};
