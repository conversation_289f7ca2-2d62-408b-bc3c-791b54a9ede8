import { Progress } from "@/shared/components/ui/progress";
import { FileText } from "lucide-react";

export interface IStepConfig {
	id: number;
	title: string;
	description: string;
	icon: React.ElementType;
	mobileTitle?: string;
	isOptional?: boolean;
}

interface IStepperHeaderProps {
	currentStep: number;
	totalSteps: number;
	progress: number;
	getCurrentStepConfig: () => IStepConfig | undefined;
}

export const StepperHeader = ({ currentStep, totalSteps, progress, getCurrentStepConfig }: IStepperHeaderProps) => {
	return (
		<div className="flex flex-col p-3 md:p-6 bg-white shadow-lg rounded-lg">
			<div className="space-y-4">
				<div className="flex items-center justify-between">
					<div className="flex gap-4">
						<div className="bg-gray-100 p-2 md:p-3 rounded-lg flex items-center justify-center">
							<FileText className="w-6 h-6 md:w-8 md:h-8 text-primary" />
						</div>
						<div>
							<h1 className="text-xl md:text-2xl font-semibold text-gray-900">Assinatura de EPI</h1>
							<p className="text-sm text-gray-600">
								Etapa {currentStep} de {totalSteps}: {getCurrentStepConfig()?.description}
							</p>
						</div>
					</div>
					<div className="text-right md:block hidden">
						<div className="text-2xl font-bold text-pormade">{Math.round(progress)}%</div>
						<div className="text-xs text-gray-600">Concluído</div>
					</div>
				</div>
				<div className="md:block hidden">
					<Progress value={progress} className="h-2" />
				</div>
			</div>
		</div>
	);
};
