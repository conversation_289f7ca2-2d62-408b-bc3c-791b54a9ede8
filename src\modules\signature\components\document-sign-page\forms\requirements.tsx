"use client";

import { rubricSvgString } from "@/modules/signature/states/rubric/rubric-svg.state";
import { mainRequisitToSignAtom } from "@/modules/signature/states/signature/main-requisit-to-sign.state";
import { useAtomValue } from "jotai";
import { BadgeCheck, BadgeX } from "lucide-react";
import { useFormContext } from "react-hook-form";

interface RequirementProps {
	label: string;
	condition: boolean;
	ariaLabel: string;
}

const Requirement: React.FC<RequirementProps> = ({ label, condition, ariaLabel }) => {
	return (
		<div className="flex items-center gap-2 w-full">
			{condition ? (
				<BadgeCheck className="w-6 h-6 text-pormade hover:text-green-800 cursor-pointer" aria-label={ariaLabel} />
			) : (
				<BadgeX className="w-6 h-6 text-red-600 hover:text-red-800 cursor-pointer" aria-label={ariaLabel} />
			)}
			<p className="flex-1 text-sm md:text-base">{label}</p>
		</div>
	);
};

const RequirementsActions = () => {
	const mainRequisitToSign = useAtomValue(mainRequisitToSignAtom);
	const { watch } = useFormContext();
	const terms1 = watch("terms1");
	const cpf = watch("cpf");
	const rubricSvg = useAtomValue(rubricSvgString);

	return (
		<div className="w-full flex flex-col gap-4 p-4">
			<Requirement label="Ler o documento até o final, ou baixá-lo" condition={mainRequisitToSign} ariaLabel="Documento lido" />
			<Requirement label="Declarar que concorda em realizar a assinatura do documento" condition={terms1 === true} ariaLabel="Termos aceitos" />
			<Requirement label="Informar documento de identificação (CPF/CNPJ)" condition={cpf.length > 11} ariaLabel="CPF/CNPJ informado" />
			<Requirement label="Cadastrar Rúbrica" condition={!!rubricSvg} ariaLabel="Cadatrar rúbrica" />
		</div>
	);
};

export default RequirementsActions;
