import { RefObject, useEffect, useState } from "react";

export const useVisibility = (ref: RefObject<HTMLElement>, threshold = 0.1) => {
	const [isVisible, setIsVisible] = useState(true);

	useEffect(() => {
		const element = ref.current;
		if (!element) return;

		const observer = new IntersectionObserver(
			entries => {
				entries.forEach(entry => setIsVisible(entry.isIntersecting));
			},
			{ threshold }
		);
		observer.observe(element);

		return () => {
			observer.unobserve(element);
		};
	}, [ref, threshold]);

	return isVisible;
};
