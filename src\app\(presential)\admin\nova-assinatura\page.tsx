"use client";

import { useNewSignaturePage } from "@/modules/presential/hooks/admin/use-new-signature-page.hook";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/shared/components/ui/card";
import { HardHat, Plus } from "lucide-react";

const NewSignaturePage = () => {
	const { handleEpiSignatureClick, handleOtherTypeClick, isOtherTypeDisabled } = useNewSignaturePage();

	return (
		<div className="space-y-6">
			{/* Header Section */}
			<div className="flex flex-col p-3 md:p-6 bg-white shadow-lg rounded-lg">
				<div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-3 md:gap-4">
					<div className="flex items-center gap-3 md:gap-4">
						<div className="bg-gray-100 p-2 md:p-3 rounded-lg flex items-center justify-center">
							<Plus className="w-6 h-6 md:w-8 md:h-8 text-pormade" />
						</div>
						<div>
							<h1 className="text-xl md:text-2xl font-semibold text-gray-900">Nova Assinatura</h1>
							<p className="text-sm md:text-base text-gray-500">Selecione o tipo de assinatura que deseja criar</p>
						</div>
					</div>
				</div>
			</div>

			{/* Selection Cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{/* EPI Signature Card */}
				<Card className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:border-pormade/40 group">
					<CardHeader className="text-center pb-4">
						<div className="mx-auto mb-4 p-4 bg-pormade/10 rounded-full w-fit group-hover:bg-pormade/20 transition-colors">
							<HardHat className="w-8 h-8 text-pormade" />
						</div>
						<CardTitle className="text-xl font-semibold text-gray-900">Assinatura de EPI</CardTitle>
						<CardDescription className="text-gray-600">
							Criar assinatura para retirada ou devolução de Equipamentos de Proteção Individual
						</CardDescription>
					</CardHeader>
					<CardContent className="pt-0">
						<Button
							onClick={handleEpiSignatureClick}
							className="w-full bg-pormade hover:bg-pormade/90 text-white font-medium py-3 rounded-lg transition-colors"
							aria-label="Criar assinatura de EPI"
						>
							Selecionar
						</Button>
					</CardContent>
				</Card>

				{/* Other Type Card */}
				<Card className="transition-all duration-200 opacity-60 cursor-not-allowed">
					<CardHeader className="text-center pb-4">
						<div className="mx-auto mb-4 p-4 bg-gray-100 rounded-full w-fit">
							<Plus className="w-8 h-8 text-gray-400" />
						</div>
						<CardTitle className="text-xl font-semibold text-gray-500">Outro Tipo</CardTitle>
						<CardDescription className="text-gray-400">Outros tipos de assinatura estarão disponíveis em breve</CardDescription>
					</CardHeader>
					<CardContent className="pt-0">
						<Button
							onClick={handleOtherTypeClick}
							disabled={isOtherTypeDisabled}
							className="w-full font-medium py-3 rounded-lg transition-colors"
							variant="secondary"
							aria-label="Outro tipo de assinatura - Em breve"
							aria-disabled="true"
						>
							Em Breve
						</Button>
					</CardContent>
				</Card>
			</div>
		</div>
	);
};

export default NewSignaturePage;
