import { usePersonForm } from "@/modules/signature/hooks/signature/sign-page/person-form.hook";
import { useSubmitSignature } from "@/modules/signature/hooks/signature/sign-page/submit-signature.hook";
import { Card } from "@/shared/components/custom/card";
import { Checkbox } from "@/shared/components/ui/checkbox";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { cpfCnpjMask } from "@/shared/lib/mask/cpf-cnpj.mask";
import { ListCheck, PencilLine, Waypoints } from "lucide-react";
import { useRef, useState } from "react";
import { Controller, FormProvider } from "react-hook-form";
import ButtonActions, { ButtonActionsRef } from "./button-submit";
import RequirementsActions from "./requirements";

export const PersonForm = () => {
	const methods = usePersonForm();

	const { onSubmitSignature } = useSubmitSignature();

	const [, setCpfCnpj] = useState("");
	const buttonActionRef = useRef<ButtonActionsRef>(null);

	const handleCnpjCpfChange = (e: React.ChangeEvent<HTMLInputElement>, onChange: (value: string) => void) => {
		const newValue = e.target.value.replace(/\D/g, "");
		const maskedValue = cpfCnpjMask(newValue);
		setCpfCnpj(maskedValue);
		onChange(maskedValue);
	};

	return (
		<>
			<FormProvider {...methods}>
				<form className="w-full flex flex-col gap-5 items-stretch h-full" onSubmit={methods.handleSubmit(onSubmitSignature)}>
					<Card Icon={Waypoints} title="Ações Requeridas">
						<div className="flex w-full items-center gap-5">
							<h2 className="text-xl font-semibold text-center text-gray-800">Dados Pessoais</h2>
						</div>
						<div className="w-full flex flex-col p-2 sm:p-0 gap-3 sm:gap-5 my-4">
							<Controller
								control={methods.control}
								name="cpf"
								render={({ field }) => (
									<div className="grid w-full items-center gap-1.5">
										<Label htmlFor="person">CPF/CNPJ</Label>
										<Input
											className="focus:outline-none disabled:cursor-not-allowed disabled:bg-gray-200 disabled:opacity-50 focus:ring-2 focus:ring-pormade "
											{...field}
											onChange={e => handleCnpjCpfChange(e, field.onChange)}
											placeholder="000.000.000-00"
										/>
										{methods.formState.errors[field.name] && (
											<span className="text-red-500">{String(methods.formState.errors[field.name]?.message)}</span>
										)}
									</div>
								)}
							/>
							<div className="items-top flex  flex-col text-center justify-center space-x-2">
								<div className="flex gap-3 items-center">
									<Controller
										control={methods.control}
										name="terms1"
										render={({ field: { onChange, onBlur, value, ref } }) => (
											<Checkbox
												id="terms1"
												className="cursor-pointer peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
												aria-required="true"
												aria-invalid={Boolean(methods.formState.errors.terms1)}
												aria-labelledby="terms1-label"
												checked={value}
												onCheckedChange={onChange}
												onBlur={onBlur}
												ref={ref}
											/>
										)}
									/>
									<div className="grid gap-1.5 leading-none">
										<label
											htmlFor="terms1"
											className="text-sm  cursor-pointer font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
										>
											Declaro que li e concordo em assinar o documento
										</label>
									</div>
								</div>
								{methods.formState.errors.terms1 && (
									<span className="text-red-500">{String(methods.formState.errors.terms1?.message)}</span>
								)}
							</div>
						</div>
					</Card>

					<Card Icon={ListCheck} title="Requisitos para Assinar">
						<RequirementsActions />
					</Card>

					<Card Icon={PencilLine} title="Confirmar Assinatura">
						<ButtonActions onSubmit={methods.handleSubmit(onSubmitSignature)} ref={buttonActionRef} />
						<p className="text-xs text-gray-500 mt-4 text-center">
							* Todos os requisitos listados acima devem ser atendidos para completar a assinatura
						</p>
					</Card>
				</form>
			</FormProvider>
		</>
	);
};
