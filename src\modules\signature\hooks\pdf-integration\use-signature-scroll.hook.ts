import { useCallback, useEffect, useRef } from "react";
import { ISignaturePosition } from "../../types/signature/signature-overlay.interface";

/**
 * Hook para scroll automático para posição de assinatura
 * Responsabilidade única: gerenciar scroll para assinatura
 * Mantém a lógica de scroll separada do PDF
 */
export const useSignatureScroll = (zoom: number) => {
	const hasScrolledRef = useRef(false);

	/**
	 * Faz scroll para a posição da assinatura
	 */
	const scrollToSignature = useCallback(
		(signature: ISignaturePosition | null): void => {
			if (!signature) return;

			// Busca o container do PDF automaticamente
			let container = document.querySelector('div[style*="overflow: auto"], div[style*="overflow:auto"], .pdf-container') as HTMLElement;
			if (!container) {
				// Fallback: busca por qualquer div que contenha canvas
				container = document.querySelector("div:has(canvas[data-page])") as HTMLElement;
				if (!container) {
					// Último fallback: busca pelo body ou window
					container = document.body;
				}
			}

			const { page, x, y } = signature;
			const canvasSelector = `canvas[data-page="${page + 1}"]`;
			const canvas = document.querySelector(canvasSelector) as HTMLCanvasElement | null;
			if (!canvas) return;

			// Calcula o zoom atual baseado no tamanho do canvas
			const canvasRect = canvas.getBoundingClientRect();
			const actualZoom = canvasRect.width / canvas.width || zoom;

			const targetX = canvas.offsetLeft + x * actualZoom;
			const targetY = canvas.offsetTop + y * actualZoom;

			const { width: containerWidth, height: containerHeight } = container.getBoundingClientRect();
			const scrollLeft = targetX - containerWidth / 2;
			const scrollTop = targetY - containerHeight / 2;

			container.scrollTo({
				left: scrollLeft,
				top: scrollTop,
				behavior: "smooth",
			});
		},
		[zoom]
	);

	/**
	 * Scroll automático quando assinatura é posicionada
	 */
	const autoScrollToSignature = useCallback(
		(signaturePosition: ISignaturePosition | null, signatureSvg: string | null, forceScroll: boolean = false) => {
			console.log("🔄 autoScrollToSignature chamado:", { signaturePosition, signatureSvg: !!signatureSvg, forceScroll });

			if (!signaturePosition || !signatureSvg) {
				hasScrolledRef.current = false;
				return;
			}

			if (hasScrolledRef.current && !forceScroll) {
				console.log("⏭️ Scroll já realizado, pulando");
				return;
			}

			console.log("📜 Iniciando scroll automático");
			// Aguarda um pouco para garantir que o PDF foi renderizado
			setTimeout(() => {
				scrollToSignature(signaturePosition);
				hasScrolledRef.current = true;
			}, 100);
		},
		[scrollToSignature]
	);

	/**
	 * Reset do estado de scroll
	 */
	const resetScrollState = useCallback(() => {
		hasScrolledRef.current = false;
	}, []);

	return {
		scrollToSignature,
		autoScrollToSignature,
		resetScrollState,
	};
};

/**
 * Hook que combina scroll automático com estados de assinatura
 */
export const useAutoSignatureScroll = (zoom: number, signaturePosition: ISignaturePosition | null, signatureSvg: string | null) => {
	const { autoScrollToSignature, resetScrollState } = useSignatureScroll(zoom);

	// Auto scroll quando assinatura muda
	useEffect(() => {
		autoScrollToSignature(signaturePosition, signatureSvg);
	}, [signaturePosition, signatureSvg, autoScrollToSignature]);

	// Reset quando componente desmonta ou assinatura é removida
	useEffect(() => {
		if (!signaturePosition || !signatureSvg) {
			resetScrollState();
		}
	}, [signaturePosition, signatureSvg, resetScrollState]);

	return {
		resetScrollState,
	};
};
