import React from "react";
import { IStepConfig } from "./stepper-header";
import { StepItem } from "./step-item";

interface IStepperMobileProps {
	stepConfig: IStepConfig[];
	currentStep: number;
	progress: number;
	getCurrentStepConfig: () => IStepConfig | undefined;
	getStepStatus: (stepId: number) => "active" | "completed" | "pending";
	handleStepChange: (stepId: number) => void;
	getStepIcon: (stepId: number) => React.ElementType;
}

export const StepperMobile = ({
	stepConfig,
	currentStep,
	progress,
	getCurrentStepConfig,
	getStepStatus,
	handleStepChange,
	getStepIcon,
}: IStepperMobileProps) => {
	return (
		<div className="md:hidden">
			<div className="p-3 sm:p-4 bg-white shadow-lg rounded-lg">
				<div className="mb-3 sm:mb-4 p-3 sm:p-4 bg-pormade/5 border border-pormade/20 rounded-lg">
					<div className="flex items-center gap-3 sm:gap-4">
						<div className="relative flex-shrink-0">
							<div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center border-2 bg-white border-pormade text-pormade ring-2 sm:ring-4 ring-pormade/20 shadow-lg">
								{React.createElement(getStepIcon(currentStep), { className: "w-5 h-5 sm:w-6 sm:h-6" })}
							</div>
							<div className="absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1 w-5 h-5 sm:w-6 sm:h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white bg-pormade text-white">
								{currentStep}
							</div>
						</div>
						<div className="flex-1 min-w-0">
							<div className="flex items-start sm:items-center justify-between flex-col sm:flex-row gap-1 sm:gap-0">
								<div className="flex-1 min-w-0">
									<h3 className="font-semibold text-sm sm:text-base text-gray-900 leading-tight">
										{getCurrentStepConfig()?.mobileTitle || getCurrentStepConfig()?.title}
									</h3>
									<p className="text-xs sm:text-sm mt-0.5 sm:mt-1 text-gray-600 leading-tight">
										{getCurrentStepConfig()?.description}
									</p>
								</div>
								<div className="flex items-center gap-1 flex-shrink-0">
									<div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-pormade rounded-full animate-pulse"></div>
									<span className="text-xs sm:text-sm text-pormade font-medium">Atual</span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="space-y-2 sm:space-y-3">
					<div className="flex items-center justify-between text-xs sm:text-sm text-gray-600">
						<span className="font-medium">Progresso</span>
						<span className="font-semibold">{Math.round(progress)}% concluído</span>
					</div>
					<div className="flex items-center justify-between gap-1 sm:gap-2">
						{stepConfig.map((step, idx) => {
							const status = getStepStatus(step.id);
							const isClickable = step.id <= currentStep || status === "completed";
							const isLast = idx === stepConfig.length - 1;

							return (
								<StepItem
									key={step.id}
									step={step}
									status={status}
									isLast={isLast}
									isClickable={isClickable}
									currentStep={currentStep}
									variant="mobile"
									onStepClick={handleStepChange}
								/>
							);
						})}
					</div>
				</div>
			</div>
		</div>
	);
};
