export enum TypeRoutesEnum {
	PUBLIC = "PUBLIC",
}

export const routesMap: Record<string, TypeRoutesEnum[]> = {
	"^/$": [TypeRoutesEnum.PUBLIC],
	"^/assinaturas(/.*)?$": [TypeRoutesEnum.PUBLIC],
	"^/assinaturas/validar(/.*)?$": [TypeRoutesEnum.PUBLIC],
	"^/assinaturas/acesso(/.*)?$": [TypeRoutesEnum.PUBLIC],
	"^/assinaturas/validar-documento(/.*)?$": [TypeRoutesEnum.PUBLIC],
	"^/admin(/.*)?$": [TypeRoutesEnum.PUBLIC],
};
