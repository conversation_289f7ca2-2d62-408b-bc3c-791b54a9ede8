import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { createTerm, ICreateTermBody } from "../../services/requests/terms/create";

export const useCreateTermMutation = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationKey: ["terms", "create"],
		mutationFn: async (body: ICreateTermBody) => {
			const res = await createTerm(body);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["terms", "all"] });
			toast.dismiss();
			toast.success("Termo criado com sucesso!");
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});
};
