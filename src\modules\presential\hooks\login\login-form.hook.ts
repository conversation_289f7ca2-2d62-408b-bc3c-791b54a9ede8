import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { adminLoginFormSchema, IAdminLoginFormSchema } from "../../validators/admin-login.form";

export const useLoginForm = () => {
	return useForm<IAdminLoginFormSchema>({
		resolver: zodResolver(adminLoginFormSchema),
		defaultValues: {
			username: "",
			password: "",
			signIn: false,
		},
	});
};
