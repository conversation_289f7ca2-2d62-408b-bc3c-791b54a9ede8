"use server";

import { IRubricRequest, IUploadRubricParams } from "@/modules/signature/types/signature/rubric.type";
import { createRequest } from "@/shared/lib/requests/create-request.lib";
import { SIGNATURE_ROUTES } from "../../endpoints";

const createRubricFile = (rubric: IRubricRequest): File => {
	const encoder = new TextEncoder();
	const svgBuffer = encoder.encode(rubric.content);
	return new File([svgBuffer], rubric.name, { type: rubric.type });
};

export const uploadRubricRequest = async ({ documentId, rubric }: IUploadRubricParams) => {
	const file = createRubricFile(rubric);
	const formData = new FormData();
	formData.append("rubrica", file);

	return await createRequest({
		method: "POST",
		path: SIGNATURE_ROUTES.RUBRIC_UPLOAD({ documentId }),
		body: formData,
	});
};
