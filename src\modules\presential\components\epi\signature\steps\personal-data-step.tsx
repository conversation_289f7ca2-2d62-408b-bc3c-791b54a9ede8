import { useEpiSignaturePage } from "@/modules/presential/hooks/epi/use-epi-signature-page.hook";
import { Button } from "@/shared/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/shared/components/ui/card";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { cn } from "@/shared/lib/utils";
import { Camera, Upload, User, X } from "lucide-react";
import Image from "next/image";
import { useCallback, useRef, useState } from "react";

export const PersonalDataStep = () => {
	const { signatureData, updateSignatureData } = useEpiSignaturePage();
	const [formData, setFormData] = useState({
		name: signatureData.personName || "",
		cpf: signatureData.personCpf || "",
	});
	const [photo, setPhoto] = useState<File | null>(signatureData.photo || null);
	const [photoPreview, setPhotoPreview] = useState<string | null>(null);
	const [errors, setErrors] = useState<Record<string, string>>({});

	const fileInputRef = useRef<HTMLInputElement>(null);
	const cameraInputRef = useRef<HTMLInputElement>(null);

	const validateCPF = (cpf: string): boolean => {
		const cleanCPF = cpf.replace(/\D/g, "");
		if (cleanCPF.length !== 11) return false;

		if (/^(\d)\1{10}$/.test(cleanCPF)) return false;

		let sum = 0;
		for (let i = 0; i < 9; i++) {
			sum += parseInt(cleanCPF.charAt(i)) * (10 - i);
		}
		let remainder = (sum * 10) % 11;
		if (remainder === 10 || remainder === 11) remainder = 0;
		if (remainder !== parseInt(cleanCPF.charAt(9))) return false;

		sum = 0;
		for (let i = 0; i < 10; i++) {
			sum += parseInt(cleanCPF.charAt(i)) * (11 - i);
		}
		remainder = (sum * 10) % 11;
		if (remainder === 10 || remainder === 11) remainder = 0;
		if (remainder !== parseInt(cleanCPF.charAt(10))) return false;

		return true;
	};

	const formatCPF = (value: string): string => {
		const cleanValue = value.replace(/\D/g, "");
		return cleanValue
			.replace(/(\d{3})(\d)/, "$1.$2")
			.replace(/(\d{3})(\d)/, "$1.$2")
			.replace(/(\d{3})(\d{1,2})/, "$1-$2")
			.replace(/(-\d{2})\d+?$/, "$1");
	};

	const handleInputChange = (field: string, value: string) => {
		let formattedValue = value;

		if (field === "cpf") {
			formattedValue = formatCPF(value);
		}

		setFormData(prev => ({ ...prev, [field]: formattedValue }));

		if (errors[field]) {
			setErrors(prev => ({ ...prev, [field]: "" }));
		}

		updateSignatureData({
			[field === "name" ? "personName" : "personCpf"]: formattedValue,
		});
	};

	const handlePhotoUpload = useCallback(
		(file: File) => {
			if (file && file.type.startsWith("image/")) {
				setPhoto(file);

				const reader = new FileReader();
				reader.onload = e => {
					setPhotoPreview(e.target?.result as string);
				};
				reader.readAsDataURL(file);

				updateSignatureData({ photo: file });
			}
		},
		[updateSignatureData]
	);

	const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (file) {
			handlePhotoUpload(file);
		}
	};

	const handleRemovePhoto = () => {
		setPhoto(null);
		setPhotoPreview(null);
		updateSignatureData({ photo: undefined });
		if (fileInputRef.current) fileInputRef.current.value = "";
		if (cameraInputRef.current) cameraInputRef.current.value = "";
	};

	const isFormValid = formData.name.trim().length >= 2 && validateCPF(formData.cpf) && photo !== null;
	return (
		<div className="space-y-6">
			<div className="text-center space-y-2">
				<div className="w-12 h-12 bg-pormade/10 rounded-full flex items-center justify-center mx-auto">
					<User className="w-6 h-6 text-pormade" />
				</div>
				<h1 className="text-2xl font-bold text-gray-900">Dados Pessoais</h1>
				<p className="text-sm text-gray-600 max-w-md mx-auto">Preencha os dados pessoais e adicione uma foto para identificação.</p>
			</div>
			<Card>
				<CardHeader className="pb-4">
					<CardTitle className="flex items-center gap-2 text-pormade">
						<User className="w-5 h-5" />
						Informações Pessoais
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<div className="space-y-4">
							<div className="space-y-2">
								<Label htmlFor="name">Nome Completo *</Label>
								<Input
									id="name"
									type="text"
									placeholder="Digite o nome completo"
									value={formData.name}
									onChange={e => handleInputChange("name", e.target.value)}
									className={cn("transition-colors", errors.name ? "border-red-500 focus:border-red-500" : "")}
								/>
								{errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
							</div>

							<div className="space-y-2">
								<Label htmlFor="cpf">CPF *</Label>
								<Input
									id="cpf"
									type="text"
									placeholder="000.000.000-00"
									value={formData.cpf}
									onChange={e => handleInputChange("cpf", e.target.value)}
									maxLength={14}
									className={cn("transition-colors", errors.cpf ? "border-red-500 focus:border-red-500" : "")}
								/>
								{errors.cpf && <p className="text-sm text-red-600">{errors.cpf}</p>}
							</div>
						</div>
						<div className="space-y-4">
							<div className="space-y-2">
								<Label className="flex items-center gap-2">
									<Camera className="w-4 h-4" />
									Foto *
								</Label>
								{photoPreview ? (
									<div className="space-y-3">
										<div className="relative">
											<Image
												src={photoPreview}
												alt="Preview"
												width={400}
												height={192}
												className="w-full h-48 object-cover rounded-lg border"
											/>
											<Button variant="destructive" size="icon" className="absolute top-2 right-2" onClick={handleRemovePhoto}>
												<X className="w-4 h-4" />
											</Button>
										</div>
										<div className="flex gap-2">
											<Button variant="outline" onClick={() => fileInputRef.current?.click()} className="flex-1" size="sm">
												<Upload className="w-4 h-4 mr-2" />
												Trocar
											</Button>
											<Button variant="outline" onClick={() => cameraInputRef.current?.click()} className="flex-1" size="sm">
												<Camera className="w-4 h-4 mr-2" />
												Câmera
											</Button>
										</div>
									</div>
								) : (
									<div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
										<Camera className="w-8 h-8 text-gray-400 mx-auto mb-3" />
										<p className="text-sm text-gray-600 mb-3">Nenhuma foto selecionada</p>
										<div className="flex gap-2">
											<Button variant="outline" onClick={() => fileInputRef.current?.click()} className="flex-1" size="sm">
												<Upload className="w-4 h-4 mr-2" />
												Arquivo
											</Button>
											<Button variant="outline" onClick={() => cameraInputRef.current?.click()} className="flex-1" size="sm">
												<Camera className="w-4 h-4 mr-2" />
												Câmera
											</Button>
										</div>
									</div>
								)}
							</div>
						</div>
					</div>
					<input ref={fileInputRef} type="file" accept="image/*" onChange={handleFileSelect} className="hidden" />
					<input ref={cameraInputRef} type="file" accept="image/*" capture="user" onChange={handleFileSelect} className="hidden" />
				</CardContent>
			</Card>
			{isFormValid ? (
				<div className="bg-green-50 p-4 rounded-lg border border-green-200">
					<div className="flex items-center gap-3">
						<div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
							<User className="w-4 h-4 text-white" />
						</div>
						<div className="flex-1">
							<p className="font-medium text-green-900">Dados Validados</p>
							<p className="text-sm text-green-700">Todos os campos foram preenchidos corretamente</p>
						</div>
						<div className="w-3 h-3 bg-green-500 rounded-full" />
					</div>
				</div>
			) : (
				<div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
					<div className="flex items-center gap-3">
						<div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
							<User className="w-4 h-4 text-white" />
						</div>
						<div className="flex-1">
							<p className="font-medium text-yellow-900">Campos Pendentes</p>
							<p className="text-sm text-yellow-700">Preencha todos os campos obrigatórios para continuar</p>
						</div>
						<div className="w-3 h-3 bg-yellow-500 rounded-full" />
					</div>
				</div>
			)}
			<div className="text-center">
				<p className="text-sm text-gray-500 max-w-sm mx-auto">Ao clicar em próximo, você prosseguirá para a assinatura do documento.</p>
			</div>
		</div>
	);
};
