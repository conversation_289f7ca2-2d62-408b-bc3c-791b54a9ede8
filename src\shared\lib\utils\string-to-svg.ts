import DOMPurify from "dompurify";
import React from "react";

const CreateSvgFromString = ({ svgString }: { svgString: string }) => {
	const sanitized = DOMPurify.sanitize(svgString, { USE_PROFILES: { svg: true } });

	const parser = new DOMParser();
	const doc = parser.parseFromString(sanitized, "image/svg+xml");
	const svgElement = doc.documentElement;

	const convertNodeToElement = (node: Node, key: number): React.ReactNode => {
		if (node.nodeType === Node.ELEMENT_NODE) {
			const props: { [key: string]: string } = {};
			Array.from((node as Element).attributes).forEach(attr => {
				props[attr.name] = attr.value;
			});
			return React.createElement(
				(node as Element).tagName,
				{ ...props, key },
				...Array.from(node.childNodes).map((child, i) => convertNodeToElement(child, i))
			);
		} else if (node.nodeType === Node.TEXT_NODE) {
			return node.textContent;
		}
		return null;
	};

	return convertNodeToElement(svgElement, 0);
};

export default CreateSvgFromString;
