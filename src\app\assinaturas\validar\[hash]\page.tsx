"use client";
import { CardItem } from "@/modules/signature/components/cards/card-item";
import { TitleCard } from "@/modules/signature/components/cards/card-title";
import { SubTitleCard } from "@/modules/signature/components/cards/subtitle";
import { Loading } from "@/modules/signature/components/document-sign-page/pages/loading";
import { useValidateHashSignature } from "@/modules/signature/hooks/signature/validate-signature/verify-siganture.hook";
import { SubscriptionPageProps } from "@/modules/signature/types/pages/sign-page.type";
import { CheckCircle, CircleX } from "lucide-react";
import React from "react";

const ValidateOneSignaturePage: React.FC<SubscriptionPageProps> = ({ params }) => {
	const { hash } = React.use(params);
	const { data, isLoading } = useValidateHashSignature({ hash });

	if (isLoading) return <Loading />;

	return (
		<div className=" flex relative items-center justify-center  bg-gradient-to-tr from-gray-100  to-pormade/15">
			<div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
				{data?.success ? (
					<>
						<div className="flex items-center justify-center mb-6">
							<CheckCircle className="text-green-500 w-16 h-16" />
						</div>
						<TitleCard title={"Assinatura verificada com sucesso"} />

						<SubTitleCard title="Os detalhes da assinatura estão listados abaixo." />
						<div className="space-y-4">
							<CardItem name="Nome" item={data?.data?.signatory?.name} />
							<CardItem name="CPF/CNPJ" item={data?.data?.signatory?.cpf_cnpj} />
							<CardItem name="Email" item={data?.data?.signatory?.email} />
							{data?.data?.signatory?.phone && <CardItem name="Telefone" item={data?.data?.signatory?.phone} />}
							{data.data.representative && (
								<>
									<CardItem name="Representante" item={data?.data?.representative?.name} />
									<CardItem name="CPF do Representante" item={data?.data?.representative?.cpf} />
								</>
							)}
							<CardItem
								name="Data de Registro"
								item={
									data?.data?.registerDate
										? new Date(data?.data?.registerDate).toLocaleString("pt-BR", { dateStyle: "short", timeStyle: "short" })
										: ""
								}
							/>
							<CardItem
								name="Data de Assinatura"
								item={
									data?.data?.signDate
										? new Date(data?.data?.signDate).toLocaleString("pt-BR", { dateStyle: "short", timeStyle: "short" })
										: ""
								}
							/>
						</div>
					</>
				) : (
					<>
						<div className="flex items-center justify-center mb-6">
							<CircleX className="text-red-500 w-16 h-16" />
						</div>
						<TitleCard title={"Assinatura inválida ou não encontrada"} />
						<SubTitleCard title="Verifique o hash inserido e tente novamente." />
					</>
				)}
			</div>
		</div>
	);
};

export default ValidateOneSignaturePage;
