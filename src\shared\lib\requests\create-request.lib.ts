import { apiInstance, apiInstanceAdmin } from "@/shared/services/config/api";
import { ApiResponseReturn, IResponseSuccess } from "@/shared/types/requests";
import { ICreateRequest } from "@/shared/types/requests/create-request.type";
import { handleGlobalErrors } from "../errors/handle-global-errors.lib";

export const createSuccessResponse = <T>(data: T, status: number): IResponseSuccess<T> => ({
	success: true,
	data,
	status,
});

const requestFactory =
	(instance: typeof apiInstance) =>
	async <TSuccess, TRequest = unknown>(params: ICreateRequest<TRequest>): Promise<ApiResponseReturn<TSuccess>> => {
		try {
			const { path, method, body, headers, ...rest } = params;
			const requestHeaders = body instanceof FormData ? { ...headers, "Content-Type": "multipart/form-data" } : headers;

			const { data, status } = await instance.request<TSuccess>({
				url: path,
				method,
				data: body,
				headers: requestHeaders,
				...rest,
			});
			return createSuccessResponse(data, status);
		} catch (error: unknown) {
			return handleGlobalErrors(error);
		}
	};

export const createRequest = requestFactory(apiInstance);
export const createRequestAdmin = requestFactory(apiInstanceAdmin);
