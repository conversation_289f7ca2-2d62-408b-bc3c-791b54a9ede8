import { signature<PERSON><PERSON><PERSON><PERSON>, updateSignatureData<PERSON>tom } from "@/modules/presential/atoms/epi-signature.atoms";
import { useFindAllTermsQuery } from "@/modules/presential/hooks/terms/find-all-terms-query.hook";
import { ITerm } from "@/modules/presential/services/requests/terms/find-all";
import { ViewTermModal } from "@/modules/presential/components/terms/modals/view-term";
import { Card, CardContent } from "@/shared/components/ui/card";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { Button } from "@/shared/components/ui/button";
import { cn } from "@/shared/lib/utils";
import { useAtomValue, useSetAtom } from "jotai";
import { Check, FileText, Eye } from "lucide-react";
import { useState } from "react";

const SectionHeader = () => (
	<div className="text-center mb-6 px-4">
		<div className="inline-flex items-center justify-center w-16 h-16 bg-pormade/10 rounded-2xl mb-3">
			<FileText className="w-8 h-8 text-pormade" />
		</div>
		<h2 className="text-xl font-bold text-gray-900 mb-1">Selecione o Termo</h2>
		<p className="text-gray-600 leading-relaxed max-w-sm mx-auto">Escolha o termo para assinatura de EPI</p>
	</div>
);

const LoadingSkeleton = () => (
	<div className="space-y-6">
		<SectionHeader />
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
			{Array.from({ length: 6 }).map((_, index) => (
				<Card key={index} className="h-24 p-4">
					<div className="flex items-center gap-4 h-full animate-pulse">
						<Skeleton className="h-10 w-10 rounded-lg" />
						<div className="flex-1 space-y-3">
							<Skeleton className="h-4 w-full" />
							<Skeleton className="h-3 w-2/3" />
						</div>
					</div>
				</Card>
			))}
		</div>
	</div>
);

const EmptyState = () => (
	<Card className="text-center py-12 border-dashed border-2 border-gray-200 bg-gray-50/50">
		<CardContent className="space-y-4">
			<div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-2xl">
				<FileText className="w-8 h-8 text-gray-400" />
			</div>
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-2">Nenhum termo encontrado</h3>
				<p className="text-gray-500 max-w-sm mx-auto leading-relaxed">
					Não há termos disponíveis para seleção no momento. Entre em contato com o administrador.
				</p>
			</div>
		</CardContent>
	</Card>
);

interface TermCardProps {
	term: ITerm;
	isSelected: boolean;
	onSelect: (term: ITerm) => void;
}

const TermCard = ({ term, isSelected, onSelect }: TermCardProps) => {
	const [isViewModalOpen, setIsViewModalOpen] = useState(false);

	return (
		<>
			<Card
				className={cn(
					"cursor-pointer transition-all duration-200 hover:shadow-lg active:scale-[0.98]",
					"h-28 relative overflow-hidden rounded-2xl group",
					isSelected
						? "border-pormade bg-gradient-to-r from-pormade/10 to-pormade/5 shadow-md ring-2 ring-pormade/30"
						: "border-gray-200 bg-white hover:border-pormade/40"
				)}
				onClick={() => onSelect(term)}
			>
				{isSelected && (
					<div className="absolute top-3 right-3 z-10">
						<div className="w-6 h-6 bg-pormade rounded-full flex items-center justify-center shadow-lg border-2 border-white">
							<Check className="w-4 h-4 text-white" />
						</div>
					</div>
				)}

				<CardContent className="flex items-center gap-4 p-4 h-full">
					<div
						className={cn(
							"p-3 rounded-lg flex-shrink-0 transition-colors duration-200 shadow-sm",
							isSelected ? "bg-pormade text-white" : "bg-gray-100 text-pormade group-hover:bg-pormade/10"
						)}
					>
						<FileText className="w-6 h-6" />
					</div>

					<div className="flex-1 min-w-0">
						<h3 className={cn("font-semibold leading-tight mb-1 truncate", isSelected ? "text-pormade" : "text-gray-900")}>
							{term.title || term.fileName}
						</h3>
						<p className="text-xs text-gray-500 font-mono mb-0.5">ID: #{term.id}</p>
						<p className="text-xs text-gray-400">{new Date(term.createDate).toLocaleDateString("pt-BR")}</p>
					</div>

					<Button
						variant="outline"
						size="sm"
						className="h-9 px-3 flex items-center gap-2 border-pormade/30 text-pormade hover:bg-pormade/10"
						onClick={e => {
							e.stopPropagation();
							setIsViewModalOpen(true);
						}}
					>
						<Eye className="w-4 h-4" />
						<span className="hidden sm:inline">Visualizar</span>
					</Button>
				</CardContent>
			</Card>
			<ViewTermModal isOpen={isViewModalOpen} onClose={() => setIsViewModalOpen(false)} term={term} />
		</>
	);
};

export const TermSelectionStep = () => {
	const { data: termsData, isLoading } = useFindAllTermsQuery();
	const signatureData = useAtomValue(signatureDataAtom);
	const updateSignatureData = useSetAtom(updateSignatureDataAtom);

	const terms = termsData?.success ? termsData.data : [];
	const selectedTermId = signatureData.selectedTermId;

	const handleSelectTerm = (term: ITerm) => {
		updateSignatureData({ selectedTermId: term.id });
	};

	if (isLoading) {
		return <LoadingSkeleton />;
	}

	return (
		<div className="space-y-6 max-w-6xl mx-auto">
			<SectionHeader />

			{terms.length === 0 ? (
				<EmptyState />
			) : (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{terms.map(term => (
						<TermCard key={term.id} term={term} isSelected={selectedTermId === term.id} onSelect={handleSelectTerm} />
					))}
				</div>
			)}
		</div>
	);
};
