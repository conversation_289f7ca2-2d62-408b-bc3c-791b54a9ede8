import { useEpiSignaturePage } from "@/modules/presential/hooks/epi/use-epi-signature-page.hook";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/shared/components/ui/card";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Badge } from "@/shared/components/ui/badge";
import { cn } from "@/shared/lib/utils";
import { Check, Minus, Plus, Shield, Users } from "lucide-react";
import { useState, useEffect, useCallback, useMemo } from "react";

interface IEpiItem {
	id: number;
	name: string;
	description?: string;
	category?: string;
	isSelected: boolean;
	quantity: number;
	needsReturn: boolean;
}

// Mock data - substituir por hook real
const MOCK_EPIS: IEpiItem[] = [
	{ id: 1, name: "Capacete de Segurança", description: "Proteção craniana", category: "Cabeça", isSelected: false, quantity: 1, needsReturn: true },
	{ id: 2, name: "Óculos de Proteção", description: "Proteção ocular", category: "Olhos", isSelected: false, quantity: 1, needsReturn: true },
	{ id: 3, name: "Luvas de Segurança", description: "Proteção das mãos", category: "Mãos", isSelected: false, quantity: 1, needsReturn: false },
	{ id: 4, name: "Botina de Segurança", description: "Proteção dos pés", category: "Pés", isSelected: false, quantity: 1, needsReturn: true },
	{ id: 5, name: "Colete Refletivo", description: "Sinalização e proteção", category: "Tronco", isSelected: false, quantity: 1, needsReturn: true },
	{ id: 6, name: "Protetor Auricular", description: "Proteção auditiva", category: "Ouvidos", isSelected: false, quantity: 1, needsReturn: false },
];

export const EpiSelectionStep = () => {
	const { signatureData, updateSignatureData } = useEpiSignaturePage();
	const [epis, setEpis] = useState<IEpiItem[]>(MOCK_EPIS);

	const [signatoryCount, setSignatoryCount] = useState(() => {
		return signatureData.signatoryCount || 1;
	});

	useEffect(() => {
		if (!signatureData.signatoryCount) {
			const timer = setTimeout(() => {
				updateSignatureData({
					signatoryCount: 1,
					selectedEpis: [],
				});
			}, 0);
			return () => clearTimeout(timer);
		}
	}, [signatureData.signatoryCount, updateSignatureData]);

	const selectedEpis = useMemo(() => epis.filter(epi => epi.isSelected), [epis]);
	const hasSelectedEpis = selectedEpis.length > 0;

	const handleEpiToggle = useCallback(
		(epiId: number) => {
			setEpis(prev => {
				const newEpis = prev.map(epi => (epi.id === epiId ? { ...epi, isSelected: !epi.isSelected } : epi));
				setTimeout(() => {
					const selectedEpisUpdated = newEpis.filter(epi => epi.isSelected);
					updateSignatureData({
						selectedEpis: selectedEpisUpdated,
						signatoryCount: signatoryCount,
					});
				}, 0);

				return newEpis;
			});
		},
		[signatoryCount, updateSignatureData]
	);

	const handleQuantityChange = useCallback(
		(epiId: number, newQuantity: number) => {
			if (newQuantity < 1) return;
			setEpis(prev => {
				const newEpis = prev.map(epi => (epi.id === epiId ? { ...epi, quantity: newQuantity } : epi));

				setTimeout(() => {
					const selectedEpisUpdated = newEpis.filter(epi => epi.isSelected);
					updateSignatureData({
						selectedEpis: selectedEpisUpdated,
						signatoryCount: signatoryCount,
					});
				}, 0);

				return newEpis;
			});
		},
		[signatoryCount, updateSignatureData]
	);

	const handleReturnToggle = useCallback(
		(epiId: number) => {
			setEpis(prev => {
				const newEpis = prev.map(epi => (epi.id === epiId ? { ...epi, needsReturn: !epi.needsReturn } : epi));

				setTimeout(() => {
					const selectedEpisUpdated = newEpis.filter(epi => epi.isSelected);
					updateSignatureData({
						selectedEpis: selectedEpisUpdated,
						signatoryCount: signatoryCount,
					});
				}, 0);

				return newEpis;
			});
		},
		[signatoryCount, updateSignatureData]
	);
	const handleSignatoryCountChange = useCallback(
		(newCount: number) => {
			if (newCount < 1) return;
			setSignatoryCount(newCount);

			setTimeout(() => {
				updateSignatureData({
					signatoryCount: newCount,
					selectedEpis: selectedEpis,
				});
			}, 0);
		},
		[selectedEpis, updateSignatureData]
	);

	const categories = Array.from(new Set(epis.map(epi => epi.category))).filter(Boolean);

	return (
		<div className="space-y-6">
			<div className="text-center space-y-2">
				<div className="w-12 h-12 bg-pormade/10 rounded-full flex items-center justify-center mx-auto">
					<Shield className="w-6 h-6 text-pormade" />
				</div>
				<h1 className="text-2xl font-bold text-gray-900">Seleção de EPIs</h1>
				<p className="text-sm text-gray-600 max-w-md mx-auto">Escolha os EPIs que serão entregues e configure as quantidades necessárias.</p>
			</div>
			<Card>
				<CardHeader className="pb-4">
					<CardTitle className="flex items-center gap-2 text-pormade">
						<Users className="w-5 h-5" />
						Quantidade de Signatários
					</CardTitle>
					<CardDescription>Quantas pessoas receberão os mesmos EPIs selecionados?</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
						<div className="flex items-center gap-4">
							<Button
								variant="outline"
								size="icon"
								onClick={() => handleSignatoryCountChange(signatoryCount - 1)}
								disabled={signatoryCount <= 1}
								className="h-10 w-10"
							>
								<Minus className="w-4 h-4" />
							</Button>
							<div className="flex-1 max-w-20">
								<Input
									type="number"
									value={signatoryCount}
									onChange={e => handleSignatoryCountChange(Number(e.target.value))}
									min="1"
									className="text-center h-10"
								/>
							</div>
							<Button
								variant="outline"
								size="icon"
								onClick={() => handleSignatoryCountChange(signatoryCount + 1)}
								className="h-10 w-10"
							>
								<Plus className="w-4 h-4" />
							</Button>
							<Badge className="bg-blue-600 text-white font-bold">{signatoryCount}</Badge>
						</div>
					</div>
				</CardContent>
			</Card>

			{categories.map(category => (
				<Card key={category}>
					<CardHeader className="pb-4">
						<CardTitle className="flex items-center gap-2 text-pormade">
							<Shield className="w-5 h-5" />
							{category}
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-3">
						{epis
							.filter(epi => epi.category === category)
							.map(epi => (
								<div
									key={epi.id}
									className={cn(
										"p-3 rounded-lg border cursor-pointer transition-all duration-200",
										epi.isSelected ? "bg-pormade/5 border-pormade shadow-sm" : "bg-gray-50 border-gray-200 hover:border-gray-300"
									)}
									onClick={() => handleEpiToggle(epi.id)}
								>
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-3">
											<div className="w-8 h-8 bg-pormade/10 rounded-lg flex items-center justify-center">
												{epi.isSelected ? (
													<Check className="w-4 h-4 text-pormade" />
												) : (
													<Shield className="w-4 h-4 text-pormade" />
												)}
											</div>
											<div>
												<p className="font-medium text-gray-900">{epi.name}</p>
												{epi.description && <p className="text-xs text-gray-500">{epi.description}</p>}
											</div>
										</div>
										{epi.isSelected && (
											<Badge variant="secondary" className="bg-pormade/10 text-pormade">
												{epi.quantity}x
											</Badge>
										)}
									</div>

									{epi.isSelected && (
										<div className="mt-4 pt-4 border-t border-gray-200 space-y-4">
											<div className="flex items-center justify-between">
												<Label className="text-sm font-medium">Quantidade por pessoa:</Label>
												<div className="flex items-center gap-2">
													<Button
														variant="outline"
														size="sm"
														onClick={e => {
															e.stopPropagation();
															handleQuantityChange(epi.id, epi.quantity - 1);
														}}
														disabled={epi.quantity <= 1}
													>
														<Minus className="w-3 h-3" />
													</Button>
													<Input
														type="number"
														value={epi.quantity}
														onChange={e => {
															e.stopPropagation();
															handleQuantityChange(epi.id, Number(e.target.value));
														}}
														min="1"
														className="w-16 text-center"
														onClick={e => e.stopPropagation()}
													/>
													<Button
														variant="outline"
														size="sm"
														onClick={e => {
															e.stopPropagation();
															handleQuantityChange(epi.id, epi.quantity + 1);
														}}
													>
														<Plus className="w-3 h-3" />
													</Button>
												</div>
											</div>
											<div className="flex items-center justify-between">
												<Label className="text-sm font-medium">Necessita devolução:</Label>
												<Button
													variant={epi.needsReturn ? "default" : "outline"}
													size="sm"
													onClick={e => {
														e.stopPropagation();
														handleReturnToggle(epi.id);
													}}
													className={epi.needsReturn ? "bg-pormade hover:bg-pormade/90" : ""}
												>
													{epi.needsReturn ? "Sim" : "Não"}
												</Button>
											</div>
										</div>
									)}
								</div>
							))}
					</CardContent>
				</Card>
			))}
			{hasSelectedEpis && (
				<Card>
					<CardHeader className="pb-4">
						<CardTitle className="flex items-center gap-2 text-pormade">
							<Shield className="w-5 h-5" />
							Resumo da Seleção
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
							<div className="flex items-center gap-3 mb-3">
								<div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
									<Shield className="w-4 h-4 text-white" />
								</div>
								<div className="flex-1">
									<p className="font-medium text-blue-900">EPIs Selecionados</p>
									<p className="text-sm text-blue-700">
										{selectedEpis.length} EPIs para {signatoryCount} {signatoryCount === 1 ? "pessoa" : "pessoas"}
									</p>
								</div>
								<Badge className="bg-blue-600 text-white font-bold">{selectedEpis.length}</Badge>
							</div>
							<div className="space-y-2 text-xs text-blue-700">
								{selectedEpis.map(epi => (
									<div key={epi.id} className="flex justify-between items-center p-2 bg-white/50 rounded">
										<span className="font-medium">{epi.name}</span>
										<div className="flex items-center gap-2">
											<Badge variant="secondary" className="text-xs">
												{epi.quantity}x
											</Badge>
											<Badge variant={epi.needsReturn ? "destructive" : "secondary"} className="text-xs">
												{epi.needsReturn ? "Devolução" : "Consumo"}
											</Badge>
										</div>
									</div>
								))}
							</div>
						</div>
					</CardContent>
				</Card>
			)}
			{!hasSelectedEpis && (
				<div className="text-center py-8">
					<div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<Shield className="w-8 h-8 text-gray-300" />
					</div>
					<p className="font-medium text-gray-500">Nenhum EPI foi selecionado</p>
					<p className="text-sm text-gray-400">Selecione os EPIs necessários para continuar</p>
				</div>
			)}
			{hasSelectedEpis && (
				<div className="text-center">
					<p className="text-sm text-gray-500 max-w-sm mx-auto">Ao clicar em próximo, você prosseguirá para a confirmação da seleção.</p>
				</div>
			)}
		</div>
	);
};
