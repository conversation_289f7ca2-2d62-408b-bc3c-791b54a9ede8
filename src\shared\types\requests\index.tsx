export interface IResponse<T> {
	success: boolean;
	data: T;
	status: number;
}

export interface IResponseSuccess<T> extends IResponse<T> {
	success: true;
}

export interface IResponseError extends IResponse<ErrorData> {
	success: false;
}

export type ErrorData = {
	message: string;
	method?: string;
	url?: string;
};

export type ApiResponseReturn<T> = IResponseSuccess<T> | IResponseError;

export type IGlobalMessage = {
	message: string;
};
// export interface IResponse<T> {
// 	success: boolean;
// 	data: T;
// 	status: number;
// }

// export interface IResponseSuccess<T> extends IResponse<T> {
// 	success: true;
// }

// export interface IResponseError extends IResponse<ErrorData> {
// 	success: false;
// }

export interface IResponseDocumentSigned extends IResponse<IDocumentSignedReturn> {
	success: true;
	status: 400;
}

export interface IDocumentSignedReturn {
	message: string;
	signatoryName: string;
	signDate: string;
	isDocumentAvaible: boolean;
	documentBuffer: {
		type: string;
		data: ArrayBuffer;
	} | null;
}

// export type ErrorData = {
// 	message: string;
// 	method?: string;
// 	url?: string;
// };
