import { useEpiSignaturePage } from "@/modules/presential/hooks/epi/use-epi-signature-page.hook";
import { useFindTermByIdQuery } from "@/modules/presential/hooks/terms/find-term-by-id-query.hook";
import { ViewTermModal } from "@/modules/presential/components/terms/modals/view-term";
import { Button } from "@/shared/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/shared/components/ui/card";
import { cn } from "@/shared/lib/utils";
import { Check, FileText, PenTool, RotateCcw, Eye } from "lucide-react";
import { useCallback, useRef, useState } from "react";

interface EpiItem {
	name: string;
	quantity: number;
}

export const SignatureStep = () => {
	const { signatureData } = useEpiSignaturePage();
	const canvasRef = useRef<HTMLCanvasElement>(null);
	const [isDrawing, setIsDrawing] = useState(false);
	const [hasSignature, setHasSignature] = useState(false);
	const [lastPoint, setLastPoint] = useState<{ x: number; y: number } | null>(null);
	const [isViewTermModalOpen, setIsViewTermModalOpen] = useState(false);

	// Buscar o termo selecionado
	const { data: termData } = useFindTermByIdQuery(signatureData.selectedTermId || 0);
	const selectedTerm = termData?.success ? termData.data : null;

	const startDrawing = useCallback((event: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
		const canvas = canvasRef.current;
		if (!canvas) return;

		const rect = canvas.getBoundingClientRect();
		const scaleX = canvas.width / rect.width;
		const scaleY = canvas.height / rect.height;

		let clientX: number, clientY: number;

		if ("touches" in event) {
			event.preventDefault();
			clientX = event.touches[0].clientX;
			clientY = event.touches[0].clientY;
		} else {
			clientX = event.clientX;
			clientY = event.clientY;
		}

		const x = (clientX - rect.left) * scaleX;
		const y = (clientY - rect.top) * scaleY;

		setIsDrawing(true);
		setLastPoint({ x, y });
		setHasSignature(true);
	}, []);

	const draw = useCallback(
		(event: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
			if (!isDrawing || !lastPoint) return;

			const canvas = canvasRef.current;
			const ctx = canvas?.getContext("2d");
			if (!canvas || !ctx) return;

			const rect = canvas.getBoundingClientRect();
			const scaleX = canvas.width / rect.width;
			const scaleY = canvas.height / rect.height;

			let clientX: number, clientY: number;

			if ("touches" in event) {
				event.preventDefault();
				clientX = event.touches[0].clientX;
				clientY = event.touches[0].clientY;
			} else {
				clientX = event.clientX;
				clientY = event.clientY;
			}

			const x = (clientX - rect.left) * scaleX;
			const y = (clientY - rect.top) * scaleY;

			ctx.beginPath();
			ctx.moveTo(lastPoint.x, lastPoint.y);
			ctx.lineTo(x, y);
			ctx.strokeStyle = "#000";
			ctx.lineWidth = 2;
			ctx.lineCap = "round";
			ctx.lineJoin = "round";
			ctx.stroke();

			setLastPoint({ x, y });
		},
		[isDrawing, lastPoint]
	);

	const stopDrawing = useCallback(() => {
		setIsDrawing(false);
		setLastPoint(null);
	}, []);
	const clearSignature = useCallback(() => {
		const canvas = canvasRef.current;
		const ctx = canvas?.getContext("2d");
		if (!canvas || !ctx) return;

		ctx.clearRect(0, 0, canvas.width, canvas.height);
		setHasSignature(false);
	}, []);

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="text-center space-y-2">
				<div className="w-12 h-12 bg-pormade/10 rounded-full flex items-center justify-center mx-auto">
					<PenTool className="w-6 h-6 text-pormade" />
				</div>
				<h1 className="text-xl md:text-2xl font-bold text-gray-900">Assinatura do Termo</h1>
				<p className="text-sm text-gray-600 max-w-md mx-auto">Leia o termo e assine para confirmar o recebimento dos EPIs</p>
			</div>
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<Card>
					<CardHeader className="pb-4">
						<CardTitle className="flex  items-center gap-2 text-pormade">
							<FileText className="w-5 h-5" />
							Termo de Responsabilidade
						</CardTitle>

						<CardDescription>{selectedTerm ? "Leia atentamente antes de assinar" : "Carregando termo selecionado..."}</CardDescription>
					</CardHeader>
					<CardContent>
						{selectedTerm ? (
							<div className="bg-gray-50 p-3 md:p-4 rounded-lg">
								<div className="text-center text-gray-600 py-4 md:py-6">
									<FileText className="w-10 h-10 md:w-12 md:h-12 text-gray-400 mx-auto mb-3" />
									<p className="font-medium text-gray-700 mb-2">Termo Selecionado</p>
									<p className="text-sm text-gray-600 mb-3">{selectedTerm.title || selectedTerm.fileName}</p>
									<p className="text-xs text-gray-500 mb-4">
										Criado em: {new Date(selectedTerm.createDate).toLocaleDateString("pt-BR")}
									</p>
									<Button
										variant="outline"
										size="sm"
										className="border-pormade/30 text-pormade hover:bg-pormade/10 w-full sm:w-auto"
										onClick={() => setIsViewTermModalOpen(true)}
									>
										<Eye className="w-4 h-4 mr-2" />
										<span className="hidden sm:inline">Clique aqui para </span>Visualizar termo
									</Button>
								</div>
							</div>
						) : (
							<div className="bg-gray-50 p-3 md:p-4 rounded-lg">
								<div className="text-center text-gray-600 py-4 md:py-6">
									<div className="w-10 h-10 md:w-12 md:h-12 bg-gray-200 rounded-lg mx-auto mb-3 animate-pulse" />
									<p className="text-sm text-gray-500">Carregando termo selecionado...</p>
								</div>
							</div>
						)}
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="pb-4">
						<CardTitle className="flex items-center gap-2 text-pormade">
							<PenTool className="w-5 h-5" />
							Assinatura Digital
						</CardTitle>
						<CardDescription>Assine no campo abaixo usando o mouse ou toque</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="relative">
							<canvas
								ref={canvasRef}
								width={400}
								height={200}
								className={cn(
									"w-full h-40 md:h-48 border-2 border-dashed rounded-lg cursor-crosshair",
									"touch-none select-none",
									hasSignature ? "border-green-300 bg-green-50" : "border-gray-300 bg-gray-50"
								)}
								onMouseDown={startDrawing}
								onMouseMove={draw}
								onMouseUp={stopDrawing}
								onMouseLeave={stopDrawing}
								onTouchStart={startDrawing}
								onTouchMove={draw}
								onTouchEnd={stopDrawing}
							/>
							{!hasSignature && (
								<div className="absolute inset-0 flex items-center justify-center pointer-events-none">
									<div className="text-center px-4">
										<PenTool className="w-6 h-6 md:w-8 md:h-8 text-gray-400 mx-auto mb-2" />
										<p className="text-xs md:text-sm text-gray-500">Clique e arraste para assinar</p>
										<p className="text-xs text-gray-400 mt-1 hidden sm:block">Use o mouse ou toque na tela</p>
									</div>
								</div>
							)}
						</div>

						<div className="flex gap-2">
							<Button variant="outline" onClick={clearSignature} disabled={!hasSignature} className="flex-1">
								<RotateCcw className="w-4 h-4 mr-2" />
								Limpar
							</Button>
						</div>

						{hasSignature && (
							<div className="p-3 bg-green-50 border border-green-200 rounded-lg">
								<div className="flex items-center gap-2">
									<Check className="w-4 h-4 text-green-600" />
									<span className="text-sm font-medium text-green-800">Assinatura capturada com sucesso</span>
								</div>
							</div>
						)}
					</CardContent>
				</Card>
			</div>
			{hasSignature ? (
				<div className="bg-green-50 p-3 md:p-4 rounded-lg border border-green-200">
					<div className="flex items-center gap-3">
						<div className="w-6 h-6 md:w-8 md:h-8 bg-green-500 rounded-lg flex items-center justify-center flex-shrink-0">
							<Check className="w-3 h-3 md:w-4 md:h-4 text-white" />
						</div>
						<div className="flex-1 min-w-0">
							<p className="font-medium text-green-900 text-sm md:text-base">Termo Assinado</p>
							<p className="text-xs md:text-sm text-green-700">Assinatura capturada com sucesso. Processo concluído.</p>
						</div>
						<div className="w-3 h-3 bg-green-500 rounded-full flex-shrink-0" />
					</div>
				</div>
			) : (
				<div className="bg-yellow-50 p-3 md:p-4 rounded-lg border border-yellow-200">
					<div className="flex items-center gap-3">
						<div className="w-6 h-6 md:w-8 md:h-8 bg-yellow-500 rounded-lg flex items-center justify-center flex-shrink-0">
							<PenTool className="w-3 h-3 md:w-4 md:h-4 text-white" />
						</div>
						<div className="flex-1 min-w-0">
							<p className="font-medium text-yellow-900 text-sm md:text-base">Assinatura Pendente</p>
							<p className="text-xs md:text-sm text-yellow-700">Assine o termo no campo acima para continuar</p>
						</div>
						<div className="w-3 h-3 bg-yellow-500 rounded-full flex-shrink-0" />
					</div>
				</div>
			)}{" "}
			<Card className="bg-blue-50 border-blue-200">
				<CardHeader className="pb-3 md:pb-4">
					<CardTitle className="text-base md:text-lg text-pormade">Resumo dos Dados</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
						<div className="space-y-1">
							<p className="font-medium text-blue-800 mb-2">Dados Pessoais:</p>
							<p className="text-blue-700 text-xs md:text-sm">Nome: {signatureData.personName || "Não informado"}</p>
							<p className="text-blue-700 text-xs md:text-sm">CPF: {signatureData.personCpf || "Não informado"}</p>
						</div>
						<div className="space-y-1">
							<p className="font-medium text-blue-800 mb-2">EPIs Selecionados:</p>
							{signatureData.selectedEpis?.length ? (
								signatureData.selectedEpis.map((epi, index) => (
									<p key={index} className="text-blue-700 text-xs md:text-sm">
										• {(epi as EpiItem).name} ({(epi as EpiItem).quantity}x)
									</p>
								))
							) : (
								<p className="text-blue-700 text-xs md:text-sm">Nenhum EPI selecionado</p>
							)}
						</div>
					</div>
				</CardContent>
			</Card>
			<div className="text-center">
				<p className="text-sm text-gray-500 max-w-sm mx-auto">
					Ao assinar o termo, você confirma o recebimento dos EPIs e aceita as responsabilidades descritas.
				</p>
			</div>
			<ViewTermModal isOpen={isViewTermModalOpen} onClose={() => setIsViewTermModalOpen(false)} term={selectedTerm} />
		</div>
	);
};
