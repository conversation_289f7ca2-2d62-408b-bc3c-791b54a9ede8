export const SigningIllustration = () => {
	return (
		<svg viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-full">
			{/* Fundo Sutil */}
			<circle cx="200" cy="200" r="160" fill="#F8FAFC" opacity="0.8" />

			{/* Documento */}
			<g filter="url(#shadow)">
				<rect x="100" y="80" width="200" height="280" rx="12" fill="white" />
			</g>

			{/* Linhas de Texto */}
			<g opacity="0.6">
				<rect x="124" y="120" width="152" height="2" rx="1" fill="#94A3B8" />
				<rect x="124" y="140" width="152" height="2" rx="1" fill="#94A3B8" />
				<rect x="124" y="160" width="100" height="2" rx="1" fill="#94A3B8" />
			</g>

			{/* Área de Assinatura */}
			<g>
				<rect x="124" y="240" width="152" height="80" rx="6" fill="#F1F5F9" stroke="#E2E8F0" strokeWidth="1.5" />

				{/* Assinatura */}
				<path
					d="M144 280 C154 270, 164 290, 174 280 C184 270, 194 290, 204 280 C214 270, 224 290, 234 280 C244 270, 254 290, 256 280"
					stroke="#64748B"
					strokeWidth="1.5"
					strokeLinecap="round"
				/>
			</g>

			{/* Caneta Minimalista */}
			<g transform="translate(264, 265) rotate(-45)">
				<path d="M-10 6 L0 3 L0 9 L-10 6" fill="#475569" />
				<rect width="70" height="12" rx="6" fill="#475569" />
			</g>

			{/* Sombra do Documento */}
			<defs>
				<filter id="shadow" x="90" y="70" width="220" height="300" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
					<feFlood floodOpacity="0" result="BackgroundImageFix" />
					<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
					<feOffset dy="4" />
					<feGaussianBlur stdDeviation="5" />
					<feComposite in2="hardAlpha" operator="out" />
					<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
					<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_2" />
					<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_2" result="shape" />
				</filter>
			</defs>
		</svg>
	);
};
