"use client";

import { LoginForm } from "@/modules/presential/components/login/login-form";
import { LoginHeader } from "@/modules/presential/components/login/login-header";
import { LoginSidebar } from "@/modules/presential/components/login/login-sidebar";
import { useLoginForm } from "@/modules/presential/hooks/login/login-form.hook";
import { useLoginMutation } from "@/modules/presential/hooks/login/login-mutation.hook";
import { IAdminLoginFormSchema } from "@/modules/presential/validators/admin-login.form";

const LoginPage = () => {
	const {
		control,
		handleSubmit,
		formState: { errors },
	} = useLoginForm();
	const { login, isLoading } = useLoginMutation();

	const onSubmit = async (data: IAdminLoginFormSchema) => {
		login({
			...data,
		});
	};

	return (
		<div className="min-h-screen flex flex-col md:flex-row bg-gray-50">
			<LoginSidebar />
			<div className="w-full md:w-1/2 flex flex-col min-h-screen">
				<div className="flex-1 flex flex-col justify-center px-3 md:px-6 py-10 md:p-12 lg:p-16">
					<div className="w-full md:max-w-md p-2 md:p-0 md:mx-auto space-y-8">
						<LoginHeader />
						<LoginForm isLoading={isLoading} control={control} handleSubmit={handleSubmit} errors={errors} onSubmit={onSubmit} />
					</div>
				</div>
			</div>
		</div>
	);
};

export default LoginPage;
