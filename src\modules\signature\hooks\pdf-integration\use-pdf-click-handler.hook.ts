import { useCallback } from "react";
import { useSet<PERSON>tom } from "jotai";
import { signaturePositionAtom } from "../../states/signature/signature-position.state";
import { ISignaturePosition } from "../../types/signature/signature-overlay.interface";

/**
 * Hook para lidar com cliques no PDF para posicionar assinatura
 * Responsabilidade única: converter cliques em posições de assinatura
 */
export const usePdfClickHandler = () => {
	const setSignaturePosition = useSetAtom(signaturePositionAtom);

	/**
	 * Calcula a posição da assinatura baseada no clique
	 */
	const calculateSignaturePosition = useCallback(
		(event: React.MouseEvent<HTMLCanvasElement>, pageNumber: number, zoom: number): ISignaturePosition | null => {
			const canvas = event.currentTarget;
			const rect = canvas.getBoundingClientRect();

			// Posição do clique relativa ao canvas
			const x = event.clientX - rect.left;
			const y = event.clientY - rect.top;

			// Converte para coordenadas do PDF (considerando zoom)
			const pdfX = x / zoom;
			const pdfY = y / zoom;

			return {
				x: pdfX,
				y: pdfY,
				page: pageNumber - 1, // PDF usa 0-indexed
				scale: 1.2, // Escala padrão da assinatura aumentada para melhor visualização
			};
		},
		[]
	);

	/**
	 * Handler para clique no canvas do PDF
	 */
	const handleCanvasClick = useCallback(
		(event: React.MouseEvent<HTMLCanvasElement>, pageNumber: number, zoom: number) => {
			console.log("🖱️ Clique no PDF detectado - página:", pageNumber, "zoom:", zoom);
			const position = calculateSignaturePosition(event, pageNumber, zoom);
			if (position) {
				console.log("📍 Posição calculada:", position);
				setSignaturePosition(position);
			}
		},
		[calculateSignaturePosition, setSignaturePosition]
	);

	/**
	 * Limpa a posição da assinatura
	 */
	const clearSignaturePosition = useCallback(() => {
		setSignaturePosition(null);
	}, [setSignaturePosition]);

	return {
		handleCanvasClick,
		clearSignaturePosition,
	};
};
