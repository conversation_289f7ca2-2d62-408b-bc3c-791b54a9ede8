import { EpiReturnFiltersType } from "@/modules/presential/hooks/epi/use-epi-return-filters.hook";
import { Button } from "@/shared/components/ui/button";
import { Label } from "@/shared/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { Separator } from "@/shared/components/ui/separator";
import { cn } from "@/shared/lib/utils";
import { Filter, X } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { PersonSearchInput } from "./person-search-input";

interface EpiReturnFiltersProps {
	form: UseFormReturn<EpiReturnFiltersType>;
	hasActiveFilters: boolean;
}

export const EpiReturnFilters = ({ form, hasActiveFilters }: EpiReturnFiltersProps) => {
	const watchSigned = form.watch("signed");

	const onResetFilters = () => {
		form.reset();
	};

	return (
		<div className="flex flex-col max-w-full w-full bg-white shadow rounded-lg  transition-all duration-300 hover:shadow-md">
			<header className="flex p-4 items-center justify-between">
				<div className="flex items-center gap-3">
					<div className="p-2 rounded-lg bg-gray-50 text-gray-900">
						<Filter className="w-5 h-5" />
					</div>
					<h2 className="text-lg font-semibold text-gray-900">Filtros</h2>
				</div>
				{hasActiveFilters && (
					<span className="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 animate-pulse">
						Filtros ativos
					</span>
				)}
			</header>
			<Separator className="w-full" />
			<form className="p-4">
				<div className="grid grid-cols-1 md:grid-cols-5 items-end gap-4">
					<PersonSearchInput form={form} />

					<div className="space-y-2 group md:col-span-2">
						<Label className="flex items-center gap-2 text-xs font-medium text-gray-600">
							<span className="flex h-3.5 w-3.5 items-center justify-center">
								<span
									className={cn(
										"h-2 w-2 rounded-full",
										watchSigned === "true" ? "bg-green-500" : watchSigned === "false" ? "bg-amber-500" : "bg-gray-300"
									)}
								/>
							</span>
							Status da Assinatura
						</Label>
						<Select
							onValueChange={value => form.setValue("signed", value as "all" | "true" | "false" | undefined)}
							value={watchSigned as "all" | "true" | "false" | undefined}
						>
							<SelectTrigger className="h-9 bg-white text-sm transition-all border-gray-200 focus:border-pormade focus:ring focus:ring-pormade">
								<SelectValue placeholder="Selecione o status" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Todos os status</SelectItem>
								<SelectItem value="true">Assinado</SelectItem>
								<SelectItem value="false">Pendente</SelectItem>
							</SelectContent>
						</Select>
					</div>

					{hasActiveFilters && (
						<div className="flex justify-end md:col-span-1 pt-2 md:pt-0">
							<Button
								type="button"
								variant="outline"
								size="sm"
								onClick={onResetFilters}
								className="h-8 px-3 text-xs font-medium border-gray-200 hover:border-red-300 hover:bg-red-50 hover:text-red-600 transition-colors w-full md:w-auto"
							>
								<X size={14} className="mr-1.5" />
								Limpar Filtros
							</Button>
						</div>
					)}
				</div>
			</form>
		</div>
	);
};
