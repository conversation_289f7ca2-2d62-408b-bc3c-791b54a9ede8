import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { IUpdateTermBody, updateTerm } from "../../services/requests/terms/update";

export const useUpdateTermMutation = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationKey: ["terms", "update"],
		mutationFn: async ({ id, body }: { id: string | number; body: IUpdateTermBody }) => {
			const res = await updateTerm(id, body);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: (_, { id }) => {
			queryClient.invalidateQueries({ queryKey: ["terms", "all"] });
			queryClient.invalidateQueries({ queryKey: ["terms", id] });
			toast.dismiss();
			toast.success("Termo atualizado com sucesso!");
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});
};
