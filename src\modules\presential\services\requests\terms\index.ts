// Re-exports
export { findAllTerms, type ITerm } from "./find-all";
export { findTermById } from "./find-by-id";
export { createTerm, type ICreateTermBody } from "./create";
export { updateTerm, type IUpdateTermBody } from "./update";
export { deleteTerm } from "./delete";
export { getTermFile } from "./get-file";
export { findAllTermTags, type ITermTag } from "./find-all-tags";
export { findTermTagById } from "./find-tag-by-id";
export { createTermTag, type ICreateTagBody } from "./create-tag";
