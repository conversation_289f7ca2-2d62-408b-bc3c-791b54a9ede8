"use server";

import { IReturnSignDocumentSucess } from "@/modules/signature/types/signature/sign-document.type";
import { createRequest } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn, IResponseDocumentSigned } from "@/shared/types/requests";
import { SIGNATURE_ROUTES } from "../../endpoints";

export const signDocumentRequest = async ({
	signatureToken,
	cpf_cnpj,
	rubricPath,
}: {
	signatureToken: string;
	cpf_cnpj: string;
	rubricPath: string;
}): Promise<ApiResponseReturn<IReturnSignDocumentSucess> | IResponseDocumentSigned> => {
	return await createRequest({
		method: "POST",
		path: SIGNATURE_ROUTES.SIGN_DOCUMENT({ signatureToken }),
		body: {
			cpf_cnpj,
			rubricPath,
		},
	});
};
