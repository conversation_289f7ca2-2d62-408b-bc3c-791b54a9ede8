import { useAtom, useAtomValue } from "jotai";
import { useCallback } from "react";
import { documentCurrentPage } from "../../states/current-page-document.state";
import { isFullPdfLoadedAtom } from "../../states/is-full-loaded.state";
import { pdfDocumentProxy } from "../../states/pdf-proxy.state";

export const usePdfStateManager = () => {
	const pdfDocument = useAtomValue(pdfDocumentProxy);
	const currentPage = useAtomValue(documentCurrentPage);
	const [isFullyLoaded, setIsFullyLoaded] = useAtom(isFullPdfLoadedAtom);

	const markAsFullyLoaded = useCallback(() => setIsFullyLoaded(true), [setIsFullyLoaded]);
	const resetLoadingState = useCallback(() => setIsFullyLoaded(false), [setIsFullyLoaded]);

	const getDocumentInfo = useCallback(
		() => (pdfDocument ? { totalPages: pdfDocument.numPages, currentPage, isFullyLoaded } : null),
		[pdfDocument, currentPage, isFullyLoaded]
	);

	const isPdfReady = useCallback(() => !!pdfDocument && isFullyLoaded, [pdfDocument, isFullyLoaded]);

	return {
		pdfDocument,
		currentPage,
		isFullyLoaded,
		markAsFullyLoaded,
		resetLoadingState,
		getDocumentInfo,
		isPdfReady,
	};
};
