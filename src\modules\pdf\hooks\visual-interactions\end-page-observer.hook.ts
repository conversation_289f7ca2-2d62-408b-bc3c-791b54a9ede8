import { mainRequisitToSignAtom } from "@/modules/signature/states/signature/main-requisit-to-sign.state";
import { useSet<PERSON>tom } from "jotai";
import { useEffect } from "react";

interface UseEndOfPdfObserverProps {
	containerRef: React.RefObject<HTMLElement | null>;
	totalPages: number;
	isFullyLoaded: boolean;
}

export const useEndOfPdfObserver = ({ containerRef, totalPages, isFullyLoaded }: UseEndOfPdfObserverProps) => {
	const setMainRequisit = useSetAtom(mainRequisitToSignAtom);

	useEffect(() => {
		if (!isFullyLoaded) return;

		const container = containerRef.current;
		if (!container) return;
		const lastPageElement = container.querySelector(`[data-page="${totalPages}"]`);
		if (!lastPageElement) return;

		const observer = new IntersectionObserver(
			entries => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						setMainRequisit(true);
					}
				});
			},
			{
				threshold: 0.5,
			}
		);

		observer.observe(lastPageElement);

		return () => {
			observer.disconnect();
		};
	}, [containerRef, totalPages, isFullyLoaded, setMainRequisit]);
};
