import { useFindAllTagsQuery } from "@/modules/presential/hooks/terms/find-all-tags-query.hook";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { AlertCircle, FileText, Tag, Upload } from "lucide-react";
import { Control, Controller, FieldErrors } from "react-hook-form";

interface TermFormProps {
	control: Control<{
		title: string;
		idTermTag: string;
		termo?: File;
	}>;
	errors: FieldErrors<{
		title: string;
		idTermTag: string;
		termo?: File;
	}>;
	mode?: "create" | "edit";
}

export const TermForm = ({ control, errors, mode = "create" }: TermFormProps) => {
	const { data: tagsData, isLoading: isTagsLoading } = useFindAllTagsQuery();

	return (
		<div className="grid gap-6 py-4">
			{mode === "create" && (
				<div className="grid gap-2">
					<div className="flex items-center gap-2">
						<Upload className="h-5 w-5 text-gray-600" />
						<Label htmlFor="term-file" className="font-medium text-gray-700">
							Arquivo do Termo (PDF)
						</Label>
					</div>
					<Controller
						name="termo"
						control={control}
						render={({ field }) => (
							<Input
								id="term-file"
								type="file"
								accept="application/pdf"
								onChange={e => {
									const file = e.target.files?.[0];
									if (file) {
										field.onChange(file);
									}
								}}
								className="rounded-lg border-gray-300 focus:ring-2 focus:ring-pormade focus:border-pormade transition-colors"
							/>
						)}
					/>
					{errors.termo && (
						<div className="flex items-center gap-1.5 text-amber-600 text-sm mt-1">
							<AlertCircle className="h-4 w-4" />
							<span>{String(errors.termo.message)}</span>
						</div>
					)}
				</div>
			)}
			<div className="grid gap-2">
				<div className="flex items-center gap-2">
					<FileText className="h-5 w-5 text-gray-600" />
					<Label htmlFor="term-title" className="font-medium text-gray-700">
						Título do Termo
					</Label>
				</div>
				<Controller
					name="title"
					control={control}
					render={({ field }) => (
						<Input
							id="term-title"
							{...field}
							className="rounded-lg border-gray-300 focus:ring-2 focus:ring-pormade focus:border-pormade transition-colors"
							placeholder="Digite o título do termo"
						/>
					)}
				/>
				{errors.title && (
					<div className="flex items-center gap-1.5 text-amber-600 text-sm mt-1">
						<AlertCircle className="h-4 w-4" />
						<span>{String(errors.title.message)}</span>
					</div>
				)}
			</div>
			<div className="grid gap-2">
				<div className="flex items-center gap-2">
					<Tag className="h-5 w-5 text-gray-600" />
					<Label htmlFor="term-tag" className="font-medium text-gray-700">
						Tag
					</Label>
				</div>
				<Controller
					name="idTermTag"
					control={control}
					render={({ field }) => (
						<Select value={field.value} onValueChange={field.onChange}>
							<SelectTrigger className="rounded-lg border-gray-300 focus:ring-2 focus:ring-pormade focus:border-pormade transition-colors h-10">
								<SelectValue placeholder="Selecione uma tag" />
							</SelectTrigger>
							<SelectContent className="rounded-lg bg-white shadow-md">
								{isTagsLoading ? (
									<div className="p-2">
										<Skeleton className="h-8 w-full mb-2" />
										<Skeleton className="h-8 w-full" />
									</div>
								) : (
									tagsData?.success &&
									tagsData?.data.map(tag => (
										<SelectItem
											key={tag.id}
											value={tag.id.toString()}
											className="cursor-pointer hover:bg-gray-100 transition-colors"
										>
											{tag.name}
										</SelectItem>
									))
								)}
							</SelectContent>
						</Select>
					)}
				/>
				{errors.idTermTag && (
					<div className="flex items-center gap-1.5 text-amber-600 text-sm mt-1">
						<AlertCircle className="h-4 w-4" />
						<span>{String(errors.idTermTag.message)}</span>
					</div>
				)}
			</div>
		</div>
	);
};
