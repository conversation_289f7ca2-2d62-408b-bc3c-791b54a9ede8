"use server";

import { ApiResponseReturn, IGlobalMessage } from "@/shared/types/requests";
import { cookies } from "next/headers";

export const removeCookie = async (name: string): Promise<ApiResponseReturn<IGlobalMessage>> => {
	try {
		const cookieStore = await cookies();
		cookieStore.delete(name);

		return {
			success: true,
			data: {
				message: `<PERSON>ie ${name} removido com sucesso`,
			},
			status: 200,
		};
	} catch (error) {
		return {
			success: false,
			data: {
				message: `Erro ao remover cookie: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
			},
			status: 500,
		};
	}
};
