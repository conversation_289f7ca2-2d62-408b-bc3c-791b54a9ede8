// Atoms para gerenciamento de estado do PDF
export { documentCurrentPage } from "./current-page-document.state";
export { isFullPdfLoadedAtom } from "./is-full-loaded.state";
export { pdfDocumentProxy } from "./pdf-proxy.state";
export { pdfOverlaysAtom } from "./pdf-overlays.state";
export { pdfRenderersAtom } from "./pdf-renderers.state";
export { signatureRendererRegisteredAtom } from "./signature-renderer-registered.state";
export { globalRenderersAtom, isRendererRegisteredAtom, registerRendererAtom } from "./global-renderers.state";
