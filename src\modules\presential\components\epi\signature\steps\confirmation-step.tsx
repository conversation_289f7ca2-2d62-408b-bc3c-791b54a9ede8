import { useSignEpiWithdrawalMutation } from "@/modules/presential/hooks/epi/sign-epi-withdrawal-mutation.hook";
import { useEpiSignaturePage } from "@/modules/presential/hooks/epi/use-epi-signature-page.hook";
import { Button } from "@/shared/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/shared/components/ui/card";
import { Separator } from "@/shared/components/ui/separator";
import { CheckCircle, FileText, Package, RotateCcw, Send, User } from "lucide-react";
import { useState } from "react";

interface ConfirmationStepProps {
	onReset: () => void;
}

export const ConfirmationStep = ({ onReset }: ConfirmationStepProps) => {
	const { signatureData } = useEpiSignaturePage();
	const { mutate: signEpiWithdrawal, isPending } = useSignEpiWithdrawalMutation();
	const [isCompleted, setIsCompleted] = useState(false);

	const handleConfirmSignature = () => {
		if (
			!signatureData.selectedTermId ||
			!signatureData.epiItems ||
			!signatureData.personCpf ||
			!signatureData.personName ||
			!signatureData.photo
		) {
			alert("Dados incompletos para assinatura");
			return;
		}

		const signatureParams = {
			idTerm: signatureData.selectedTermId,
			groupEpi: signatureData.epiItems,
			personCpf: signatureData.personCpf,
			personName: signatureData.personName,
			photo: signatureData.photo,
		};

		signEpiWithdrawal(signatureParams, {
			onSuccess: () => {
				setIsCompleted(true);
			},
		});
	};

	if (isCompleted) {
		return (
			<div className="text-center space-y-6">
				<div className="flex justify-center">
					<div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
						<CheckCircle className="w-12 h-12 text-green-600" />
					</div>
				</div>

				<div>
					<h2 className="text-2xl font-semibold text-gray-900 mb-2">Assinatura Realizada com Sucesso!</h2>
					<p className="text-gray-600">A retirada de EPIs foi assinada e registrada no sistema.</p>
				</div>

				<div className="flex justify-center gap-4">
					<Button onClick={onReset} className="flex items-center gap-2">
						<RotateCcw className="w-4 h-4" />
						Nova Assinatura
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<div className="text-center">
				<h2 className="text-2xl font-semibold text-gray-900 mb-2">Confirmação da Assinatura</h2>
				<p className="text-gray-600">Revise todos os dados antes de finalizar a assinatura da retirada de EPIs</p>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{/* Dados da Pessoa */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<User className="w-5 h-5" />
							Dados da Pessoa
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-3">
						<div>
							<span className="text-sm font-medium text-gray-700">Nome:</span>
							<p className="text-gray-900">{signatureData.personName}</p>
						</div>
						<div>
							<span className="text-sm font-medium text-gray-700">CPF:</span>
							<p className="text-gray-900">{signatureData.personCpf}</p>
						</div>
						<div>
							<span className="text-sm font-medium text-gray-700">ID da Pessoa:</span>
							<p className="text-gray-900">{signatureData.selectedPersonId}</p>
						</div>
					</CardContent>
				</Card>

				{/* Dados do Termo */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<FileText className="w-5 h-5" />
							Termo Associado
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-3">
						<div>
							<span className="text-sm font-medium text-gray-700">ID do Termo:</span>
							<p className="text-gray-900">{signatureData.selectedTermId}</p>
						</div>
						<div>
							<span className="text-sm font-medium text-gray-700">Grupo de EPIs:</span>
							<p className="text-gray-900">Grupo {signatureData.selectedEpiGroupId}</p>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Lista de EPIs */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Package className="w-5 h-5" />
						EPIs para Retirada
					</CardTitle>
					<CardDescription>{signatureData.epiItems?.length || 0} itens selecionados</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-2 max-h-48 overflow-y-auto">
						{signatureData.epiItems?.map((epi, index) => (
							<div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
								<div>
									<p className="font-medium text-gray-900">{epi.name}</p>
									{epi.description && <p className="text-sm text-gray-600">{epi.description}</p>}
								</div>
								<div className="text-right">
									<p className="text-sm font-medium text-gray-700">Qtd: {epi.quantity}</p>
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Foto Capturada */}
			{signatureData.photo && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<User className="w-5 h-5" />
							Foto da Pessoa
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="flex justify-center">
							<img
								src={URL.createObjectURL(signatureData.photo)}
								alt="Foto da pessoa"
								className="w-48 h-48 object-cover rounded-lg border"
							/>
						</div>
					</CardContent>
				</Card>
			)}

			<Separator />

			{/* Botões de Ação */}
			<div className="flex justify-center gap-4">
				<Button variant="outline" onClick={onReset} disabled={isPending} className="flex items-center gap-2">
					<RotateCcw className="w-4 h-4" />
					Cancelar
				</Button>

				<Button onClick={handleConfirmSignature} disabled={isPending} className="flex items-center gap-2 px-8">
					<Send className="w-4 h-4" />
					{isPending ? "Processando..." : "Confirmar Assinatura"}
				</Button>
			</div>
		</div>
	);
};
