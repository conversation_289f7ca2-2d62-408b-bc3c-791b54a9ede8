import { IPdfOverlay } from "@/modules/pdf/types/pdf-overlay.interface";

/**
 * Interface específica para overlay de assinatura
 * Estende IPdfOverlay seguindo o princípio Open/Closed
 */
export interface ISignatureOverlay extends IPdfOverlay {
	type: "signature";
	/**
	 * SVG da assinatura em formato string
	 */
	svgData: string;
	/**
	 * Indica se é uma prévia da assinatura
	 */
	isPreview?: boolean;
}

/**
 * Interface para dados de renderização de assinatura
 */
export interface ISignatureRenderData {
	/**
	 * SVG da assinatura
	 */
	svg: string;
	/**
	 * Se deve mostrar como prévia
	 */
	showPreview?: boolean;
	/**
	 * Cor da borda da prévia
	 */
	previewBorderColor?: string;
	/**
	 * Texto da prévia
	 */
	previewText?: string;
}

/**
 * Interface para posição de assinatura
 * Mantém compatibilidade com o estado atual
 */
export interface ISignaturePosition {
	x: number;
	y: number;
	page: number;
	scale: number;
}

/**
 * Interface para hook de integração com PDF
 */
export interface IUseSignaturePdfIntegrationHook {
	/**
	 * Adiciona uma assinatura ao PDF
	 */
	addSignatureToPage: (position: ISignaturePosition, svgData: string, isPreview?: boolean) => void;
	
	/**
	 * Remove uma assinatura do PDF
	 */
	removeSignatureFromPage: (signatureId: string) => void;
	
	/**
	 * Atualiza a posição de uma assinatura
	 */
	updateSignaturePosition: (signatureId: string, position: ISignaturePosition) => void;
	
	/**
	 * Obtém todas as assinaturas de uma página
	 */
	getSignaturesForPage: (page: number) => ISignatureOverlay[];
	
	/**
	 * Limpa todas as assinaturas
	 */
	clearAllSignatures: () => void;
}

/**
 * Props para componentes que integram assinatura com PDF
 */
export interface ISignaturePdfIntegrationProps {
	/**
	 * Callback quando uma posição de assinatura é selecionada
	 */
	onSignaturePositionSelected?: (position: ISignaturePosition) => void;
	
	/**
	 * Callback quando uma assinatura é removida
	 */
	onSignatureRemoved?: (signatureId: string) => void;
	
	/**
	 * Se deve permitir múltiplas assinaturas
	 */
	allowMultipleSignatures?: boolean;
	
	/**
	 * Se deve mostrar assinaturas como prévia
	 */
	showAsPreview?: boolean;
}
