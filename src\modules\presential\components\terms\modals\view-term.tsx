"use client";
import { useLoadPdfProxy } from "@/modules/pdf/hooks/render/pdf-load.hook";
import { useGetTermFileQuery } from "@/modules/presential/hooks/terms/get-term-file.hook";
import { ITerm } from "@/modules/presential/services/requests/terms/find-all";
import { Modal } from "@/shared/components/custom/modal";
import { Button } from "@/shared/components/ui/button";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { FileText, X } from "lucide-react";
import dynamic from "next/dynamic";

const PdfViewer = dynamic(() => import("@/modules/pdf/components/pdf"), {
	ssr: false,
	loading: () => (
		<div className="flex items-center justify-center h-96">
			<Skeleton className="w-full h-full" />
		</div>
	),
});

interface ViewTermModalProps {
	isOpen: boolean;
	onClose: () => void;
	term: ITerm | null;
}

const State = ({
	icon,
	iconClass,
	title,
	message,
	description,
}: {
	icon?: React.ReactNode;
	iconClass?: string;
	title?: string;
	message: string;
	description?: string;
}) => (
	<div className="flex items-center justify-center h-full">
		<div className="text-center max-w-md">
			{icon && <div className={iconClass}>{icon}</div>}
			{title && <p className="text-red-500 font-medium mb-2">{title}</p>}
			<p className="text-gray-500">{message}</p>
			{description && <p className="text-xs text-gray-500 mt-3">{description}</p>}
		</div>
	</div>
);

const Term = ({ id, buffer }: { id: string; buffer: ArrayBuffer }) => {
	const { isLoading, error } = useLoadPdfProxy({ id: "not-signed-page", buffer: buffer ?? new ArrayBuffer(0) });

	if (isLoading) return <State icon={<Skeleton className="w-16 h-16 rounded-full mx-auto mb-4" />} message="Processando PDF..." />;
	if (error)
		return (
			<State
				icon={<FileText className="w-16 h-16 text-red-400 mx-auto mb-4" />}
				title="Erro no documento PDF"
				message={error}
				description="Verifique se o arquivo não está corrompido ou entre em contato com o suporte."
			/>
		);

	return <PdfViewer id={id} buffer={buffer} isModal />;
};

export const ViewTermModal = ({ isOpen, onClose, term }: ViewTermModalProps) => {
	const { data, isLoading, error } = useGetTermFileQuery(term?.id || 0, !!term && isOpen);

	let content;
	if (!term) content = <State icon={<FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />} message="Termo não encontrado" />;
	else if (isLoading) content = <State icon={<Skeleton className="w-16 h-16 rounded-full mx-auto mb-4" />} message="Carregando termo..." />;
	else if (error)
		content = (
			<State
				icon={<FileText className="w-16 h-16 text-red-400 mx-auto mb-4" />}
				title="Erro ao carregar o termo"
				message="Não foi possível carregar o arquivo do termo"
			/>
		);
	else if (!data) content = <State icon={<FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />} message="Nenhum arquivo disponível" />;
	else
		content = (
			<div className="h-full">
				<Term id={`term-${term.id}`} buffer={data} />
			</div>
		);

	return (
		<Modal isOpen={isOpen} onRequestClose={onClose} shouldCloseOnOverlayClick className="max-w-6xl w-[95vw] max-h-[95vh] h-[95vh]">
			<div className="flex flex-col h-full bg-white p-2 rounded-lg shadow-lg">
				<header className="flex items-center justify-between p-4 border-b border-gray-200">
					<div className="flex items-center gap-3">
						<div className="p-2 rounded-md bg-pormade/10 text-pormade">
							<FileText size={20} className="text-pormade" />
						</div>
						<div>
							<h2 className="text-lg font-semibold text-gray-900">Visualizar Termo</h2>
							<p className="text-sm text-gray-500 truncate max-w-md" title={term?.title || ""}>
								{term?.title || "Termo não encontrado"}
							</p>
						</div>
					</div>
					<Button variant="ghost" size="sm" className="hover:bg-gray-100" onClick={onClose} aria-label="Fechar modal">
						<X size={20} />
					</Button>
				</header>
				<main className="flex-1 p-4 overflow-hidden">{content}</main>
			</div>
		</Modal>
	);
};
