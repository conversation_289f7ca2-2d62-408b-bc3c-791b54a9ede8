"use server";

import { ApiResponseReturn, IGlobalMessage } from "@/shared/types/requests";
import { cookies } from "next/headers";

export interface ICreateCookieProps {
	name: string;
	value: string;
	options?: ICookieOptions;
}

export interface ICookieOptions {
	maxAge: number;
	expires?: Date;
	path?: string;
	secure?: boolean;
	sameSite?: "strict" | "lax" | "none";
	httpOnly?: boolean;
}

const DEFAULT_COOKIE_OPTIONS = {
	path: "/",
	secure: process.env.NODE_ENV === "production",
	sameSite: "strict" as const,
	httpOnly: true,
};

export const createCookie = async (props: ICreateCookieProps): Promise<ApiResponseReturn<IGlobalMessage>> => {
	try {
		const { name, value, options } = props;

		const cookieOptions = { ...DEFAULT_COOKIE_OPTIONS, ...options };
		const cookieStore = await cookies();
		cookieStore.set(name, value, cookieOptions);

		return {
			success: true,
			data: {
				message: "Cookie criado com sucesso",
			},
			status: 200,
		};
	} catch (error) {
		return {
			success: false,
			data: {
				message: `Erro ao criar cookie: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
			},
			status: 500,
		};
	}
};
