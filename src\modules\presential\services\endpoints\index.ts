import { FindAllEpiReturnParams } from "../requests/epi/find-all-return";
import { IFindAllPersonParams } from "../requests/person/find-all";
import { buildQueryParams } from "../../../../shared/lib/requests/create-query-params";

export const ADMIN_AUTH_ENDPOINTS = {
	LOGIN: "/admin/login",
} as const;

export const TERM_ENDPOINTS = {
	CREATE: "/termo",
	GET_ALL: "/termo/all",
	GET_BY_ID: (idTermo: string | number) => `/termo/${idTermo}`,
	UPDATE: (idTermo: string | number) => `/termo/${idTermo}`,
	DELETE: (idTermo: string | number) => `/termo/${idTermo}`,
	GET_FILE: (idTermo: string | number) => `/termo/${idTermo}/arquivo`,
	CREATE_TAG: "/termo/tag",
	GET_ALL_TAGS: "/termo/tag/all",
	GET_TAG_BY_ID: (idTermoTag: string | number) => `/termo/tag/${idTermoTag}`,
} as const;

export const EPI_ENDPOINTS = {
	FIND_ALL: "/epi/all",
	GET_BY_GROUP: (idWithdrawalEpiGroup: number) => `/epi/group/${idWithdrawalEpiGroup}`,
	SIGN_EPI_WITHDRAWAL: "/assinatura/epi/assinar",
	GET_ALL_EPI_RETURN: (params: FindAllEpiReturnParams) => {
		return buildQueryParams("/assinatura/epi/devolucao/all", {
			idPerson: params.personId,
			signed: params.signed,
		});
	},
	SIGN_EPI_RETURN: "/assinatura/epi/devolucao/assinar",
} as const;

export const PERSON_ENDPOINTS = {
	FIND_ALL: (params: IFindAllPersonParams) => {
		return buildQueryParams("/pessoa/all", {
			name: params.name,
			cpf: params.cpf,
		});
	},
	FIND_BY_ID: (idPerson: number) => `/pessoa/${idPerson}`,
} as const;
