import { useRef } from "react";
import { usePdfRenderer } from "../hooks/render/render.hook";
import { IPdfOverlay } from "../types/pdf-overlay.interface";
import { PdfCanvasProps } from "../types/pdf-canvas-props.type";

interface PdfCanvasWithOverlaysProps extends PdfCanvasProps {
	overlays?: IPdfOverlay[];
	overlayData?: Record<string, unknown>;
	onRenderOverlays?: (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => Promise<void>;
}

const PdfCanvas = ({
	pdfDocument,
	pageNumber,
	zoom,
	onRenderComplete,
	forceRenderAllPages,
	overlayData,
	onRenderOverlays,
}: PdfCanvasWithOverlaysProps) => {
	const canvasRef = useRef<HTMLCanvasElement>(null);

	usePdfRenderer({
		pdfDocument,
		pageNumber,
		zoom,
		canvasRef,
		onRenderComplete,
		overlayData,
		forceRenderAllPages,
		onRenderOverlays,
	});

	return <canvas data-page={pageNumber} ref={canvasRef} style={{ display: "block", margin: "1rem auto", maxWidth: "100%" }} />;
};

export default PdfCanvas;
