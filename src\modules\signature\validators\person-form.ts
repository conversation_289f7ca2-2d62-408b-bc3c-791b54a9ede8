import { z } from "zod";

function isValidCpfCnpj(value: string): boolean {
	const cpfRegex = /^\d{3}\.\d{3}\.\d{3}-\d{2}$/;
	const cnpjRegex = /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/;
	return cpfRegex.test(value) || cnpjRegex.test(value);
}

export const personalDataSchema = z.object({
	cpf: z.string().min(14, { message: "O CPF/CNPJ é obrigatório" }).refine(isValidCpfCnpj, { message: "CPF/CNPJ inválido" }),
	terms1: z.boolean().refine(v => v, { message: "Obrigatório aceitar os termos" }),
});

export type PersonalDataSchemaType = z.infer<typeof personalDataSchema>;
