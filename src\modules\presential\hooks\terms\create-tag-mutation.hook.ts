import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { createTermTag, ICreateTagBody } from "../../services/requests/terms/create-tag";

export const useCreateTagMutation = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationKey: ["terms", "tags", "create"],
		mutationFn: async (body: ICreateTagBody) => {
			const res = await createTermTag(body);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["terms", "tags", "all"] });
			toast.dismiss();
			toast.success("Tag criada com sucesso!");
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});
};
