import { cn } from "@/shared/lib/utils";
import { LucideIcon } from "lucide-react";

interface IStepIconProps {
	icon: LucideIcon;
	status: "active" | "completed" | "pending";
	size?: "sm" | "md" | "lg";
	onClick?: () => void;
	isClickable?: boolean;
}

export const StepIcon = ({ icon: Icon, status, size = "md", onClick, isClickable = false }: IStepIconProps) => {
	const sizeClasses = {
		sm: "w-7 h-7 sm:w-8 sm:h-8",
		md: "w-10 h-10 md:w-12 md:h-12",
		lg: "w-10 h-10 md:w-12 md:h-12 lg:w-14 lg:h-14",
	};

	const iconSizeClasses = {
		sm: "w-3.5 h-3.5 sm:w-4 sm:h-4",
		md: "w-4 h-4 md:w-5 md:h-5",
		lg: "w-4 h-4 md:w-5 md:h-5 lg:w-6 lg:h-6",
	};

	return (
		<div
			className={cn(
				"rounded-lg flex items-center justify-center border-2 transition-all duration-200 shadow-lg",
				sizeClasses[size],
				status === "completed"
					? "bg-pormade border-pormade text-white"
					: status === "active"
					? "bg-white border-pormade text-pormade ring-2 md:ring-4 ring-pormade/20 z-10"
					: "bg-gray-100 border-gray-300 text-gray-400",
				isClickable && "cursor-pointer hover:scale-105"
			)}
			onClick={onClick}
		>
			<Icon className={iconSizeClasses[size]} />
		</div>
	);
};
