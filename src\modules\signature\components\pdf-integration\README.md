# Integração PDF com Assinatura - Arquitetura Refatorada

## Visão Geral

Esta refatoração implementa uma arquitetura limpa seguindo os princípios SOLID, separando claramente as responsabilidades entre os módulos PDF e Signature.

## Arquitetura

### Módulo PDF (`src/modules/pdf`)
- **Responsabilidade única**: Visualização e manipulação de documentos PDF
- **Extensibilidade**: Sistema de overlays usando padrão Strategy
- **Não conhece**: Detalhes específicos de assinatura

### Módulo Signature (`src/modules/signature`)
- **Responsabilidade única**: Lógica de processo de assinatura
- **Integração**: Usa o sistema de overlays do PDF para renderização
- **Não interfere**: Na lógica interna do PDF

## Componentes Principais

### 1. Sistema de Overlays (PDF)

```typescript
// Interface base para overlays
interface IPdfOverlay {
  id: string;
  page: number;
  x: number;
  y: number;
  scale: number;
  type: string;
}

// Renderizador usando Strategy Pattern
interface IPdfOverlayRenderer {
  supportedType: string;
  render(context: CanvasRenderingContext2D, overlay: IPdfOverlay, scale: number, data?: unknown): Promise<void>;
}
```

### 2. Integração Signature-PDF

```typescript
// Componente que integra PDF com assinatura
<PdfWithSignature
  id="document-1"
  buffer={pdfBuffer}
  isModal={false}
  showSignatureAsPreview={true}
  zoom={1.2}
/>
```

### 3. Renderizador de Assinatura

```typescript
// Implementa IPdfOverlayRenderer para assinaturas
class SignatureOverlayRenderer implements IPdfOverlayRenderer {
  supportedType = "signature";
  
  async render(context, overlay, scale, data) {
    // Renderiza assinatura com prévia, bordas, etc.
  }
}
```

## Benefícios da Refatoração

### ✅ O que está bom:

1. **Separação de Responsabilidades**: Cada módulo tem uma responsabilidade única
2. **Extensibilidade**: Sistema de overlays permite adicionar novos tipos facilmente
3. **Inversão de Dependência**: PDF não depende de Signature
4. **Composição**: Componentes podem ser combinados conforme necessário
5. **Testabilidade**: Cada parte pode ser testada independentemente

### ✅ O que foi melhorado:

1. **Acoplamento**: Removido acoplamento forte entre módulos
2. **Violação SOLID**: Corrigidas violações do princípio da responsabilidade única
3. **Lógica no JSX**: Movida para hooks especializados
4. **Reutilização**: Componentes mais reutilizáveis e flexíveis

### 🚀 Próximos passos:

1. **Testes**: Implementar testes unitários para cada hook e componente
2. **Documentação**: Adicionar JSDoc completo para todas as interfaces
3. **Performance**: Otimizar renderização de overlays para PDFs grandes
4. **Acessibilidade**: Adicionar suporte a navegação por teclado nos overlays
5. **Outros Overlays**: Implementar outros tipos (anotações, marcações, etc.)

## Exemplo de Uso

```typescript
import PdfWithSignature from "@/modules/signature/components/pdf-integration/pdf-with-signature";

function DocumentViewer({ documentBuffer }: { documentBuffer: ArrayBuffer }) {
  return (
    <PdfWithSignature
      id="main-document"
      buffer={documentBuffer}
      isModal={false}
      showSignatureAsPreview={true}
      zoom={1}
    />
  );
}
```

## Migração

Para migrar do código antigo:

1. **Substitua** `PdfViewer` por `PdfWithSignature` onde há assinatura
2. **Mantenha** `PdfViewer` para visualização simples de PDF
3. **Remova** importações diretas de estados de assinatura no PDF
4. **Use** o novo sistema de overlays para extensões futuras
