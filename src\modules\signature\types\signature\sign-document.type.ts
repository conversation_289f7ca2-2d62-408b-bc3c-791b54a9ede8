export interface IReturnSignDocumentSucess {
	message: IMessageSignDocument;
	signatoryName: string;
	signDate: string;
	signKey: string;
	isDocumentAvaible: boolean;
	documentBuffer: {
		type: string | undefined;
		data: ArrayBuffer | undefined;
	} | null;
}

export interface IMessageSignDocument {
	title: string;
	description: string;
}

export interface ISignatureTokenPayload {
	signatureToken: string;
}
