import { convertStrokesToSVG } from "@/modules/signature/lib/rubric/convert-strokes-to-svg";
import { PointerEvent, TouchEvent, useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";

export type Point = { x: number; y: number };
export type Stroke = Point[];

interface UseSignatureCanvasProps {
	onSave: (svg: string) => void;
	initialWidth?: number;
	initialHeight?: number;
}

export const useSignatureCanvas = ({ onSave, initialWidth = 300, initialHeight = 150 }: UseSignatureCanvasProps) => {
	const containerRef = useRef<HTMLDivElement>(null);
	const canvasRef = useRef<HTMLCanvasElement>(null);
	const [canvasSize, setCanvasSize] = useState({ width: initialWidth, height: initialHeight });
	const strokesRef = useRef<Stroke[]>([]);
	const currentStrokeRef = useRef<Stroke>([]);
	const [isDrawing, setIsDrawing] = useState(false);
	const [hasSignature, setHasSignature] = useState(false);

	const isTouchDevice = "ontouchstart" in window || navigator.maxTouchPoints > 0;

	useEffect(() => {
		function updateCanvasSize() {
			if (containerRef.current) {
				const containerWidth = containerRef.current.clientWidth;
				setCanvasSize({ width: containerWidth, height: containerWidth / 2 });
			}
		}
		updateCanvasSize();
		window.addEventListener("resize", updateCanvasSize);
		return () => window.removeEventListener("resize", updateCanvasSize);
	}, []);

	useEffect(() => {
		const canvas = canvasRef.current;
		if (!canvas) return;
		const context = canvas.getContext("2d");
		if (!context) return;

		const dpr = window.devicePixelRatio || 1;
		canvas.width = canvasSize.width * dpr;
		canvas.height = canvasSize.height * dpr;
		canvas.style.width = `${canvasSize.width}px`;
		canvas.style.height = `${canvasSize.height}px`;
		context.scale(dpr, dpr);

		context.lineWidth = 2;
		context.lineCap = "round";
		context.strokeStyle = "#000";
	}, [canvasSize]);

	const getCoordinates = (event: PointerEvent<HTMLCanvasElement>): Point => {
		const canvas = canvasRef.current;
		if (!canvas) return { x: 0, y: 0 };

		if (event.pointerType === "mouse") {
			const native = event.nativeEvent as MouseEvent;
			if (native.offsetX !== undefined && native.offsetY !== undefined) {
				return { x: native.offsetX, y: native.offsetY };
			}
		}
		const rect = canvas.getBoundingClientRect();
		const computedStyle = window.getComputedStyle(canvas);
		const borderLeftWidth = parseFloat(computedStyle.borderLeftWidth || "0");
		const borderTopWidth = parseFloat(computedStyle.borderTopWidth || "0");
		return {
			x: event.clientX - rect.left - borderLeftWidth,
			y: event.clientY - rect.top - borderTopWidth,
		};
	};

	const getCoordinatesFromTouch = (touch: React.Touch): Point => {
		const canvas = canvasRef.current;
		if (!canvas) return { x: 0, y: 0 };
		const rect = canvas.getBoundingClientRect();
		const computedStyle = window.getComputedStyle(canvas);
		const borderLeftWidth = parseFloat(computedStyle.borderLeftWidth || "0");
		const borderTopWidth = parseFloat(computedStyle.borderTopWidth || "0");
		return {
			x: touch.clientX - rect.left - borderLeftWidth,
			y: touch.clientY - rect.top - borderTopWidth,
		};
	};

	const isPointerInsideContainer = (event: PointerEvent<HTMLCanvasElement>): boolean => {
		const container = containerRef.current;
		if (!container) return false;
		const rect = container.getBoundingClientRect();
		const { clientX, clientY } = event;
		return clientX >= rect.left && clientX <= rect.right && clientY >= rect.top && clientY <= rect.bottom;
	};

	const startDrawingPointer = (event: PointerEvent<HTMLCanvasElement>) => {
		if (isTouchDevice && event.pointerType === "touch") return;
		event.preventDefault();
		const canvas = canvasRef.current;
		if (!canvas) return;
		canvas.setPointerCapture(event.pointerId);
		setIsDrawing(true);
		const point = getCoordinates(event);
		currentStrokeRef.current = [point];
	};

	const drawPointer = (event: PointerEvent<HTMLCanvasElement>) => {
		if (isTouchDevice && event.pointerType === "touch") return;
		event.preventDefault();
		if (!isDrawing) return;

		if (event.pointerType !== "touch" && !isPointerInsideContainer(event)) {
			endDrawingPointer(event);
			return;
		}

		const canvas = canvasRef.current;
		if (!canvas) return;
		const context = canvas.getContext("2d");
		if (!context) return;
		const point = getCoordinates(event);
		const lastPoint = currentStrokeRef.current[currentStrokeRef.current.length - 1];
		context.beginPath();
		context.moveTo(lastPoint.x, lastPoint.y);
		context.lineTo(point.x, point.y);
		context.stroke();

		currentStrokeRef.current.push(point);
	};

	const endDrawingPointer = (event?: PointerEvent<HTMLCanvasElement>) => {
		if (isTouchDevice && event && event.pointerType === "touch") return;
		if (event) {
			event.preventDefault();
			const canvas = canvasRef.current;
			if (canvas) {
				canvas.releasePointerCapture(event.pointerId);
			}
		}
		if (!isDrawing) return;
		setIsDrawing(false);
		if (currentStrokeRef.current.length > 0) {
			strokesRef.current.push(currentStrokeRef.current);
			currentStrokeRef.current = [];
			setHasSignature(true);
		}
	};

	const startDrawingTouch = (event: TouchEvent<HTMLCanvasElement>) => {
		event.preventDefault();
		setIsDrawing(true);
		const touch = event.touches[0];
		const point = getCoordinatesFromTouch(touch);
		currentStrokeRef.current = [point];
	};

	const drawTouch = (event: TouchEvent<HTMLCanvasElement>) => {
		event.preventDefault();
		if (!isDrawing) return;
		const touch = event.touches[0];
		const point = getCoordinatesFromTouch(touch);
		const canvas = canvasRef.current;
		if (!canvas) return;
		const context = canvas.getContext("2d");
		if (!context) return;
		const lastPoint = currentStrokeRef.current[currentStrokeRef.current.length - 1];
		context.beginPath();
		context.moveTo(lastPoint.x, lastPoint.y);
		context.lineTo(point.x, point.y);
		context.stroke();
		currentStrokeRef.current.push(point);
	};

	const endDrawingTouch = (event: TouchEvent<HTMLCanvasElement>) => {
		event.preventDefault();
		setIsDrawing(false);
		if (currentStrokeRef.current.length > 0) {
			strokesRef.current.push(currentStrokeRef.current);
			currentStrokeRef.current = [];
			setHasSignature(true);
		}
	};

	const clearCanvas = () => {
		const canvas = canvasRef.current;
		if (canvas) {
			const context = canvas.getContext("2d");
			context?.clearRect(0, 0, canvas.width, canvas.height);
			if (context) {
				context.lineWidth = 2;
				context.lineCap = "round";
				context.strokeStyle = "#000";
			}
		}
		strokesRef.current = [];
		setHasSignature(false);
		// if (onClear) onClear();
	};

	const MAX_SVG_SIZE = 50000;
	const saveCanvas = () => {
		const svg = convertStrokesToSVG(strokesRef.current, canvasSize.width, canvasSize.height);

		if (svg.length > MAX_SVG_SIZE) {
			toast.dismiss();
			toast.error("Sua assinatura possui muitos detalhes. Por favor, simplifique.");
			return;
		}

		onSave(svg);
	};
	return {
		containerRef,
		canvasRef,
		canvasSize,
		isDrawing,
		startDrawingPointer,
		drawPointer,
		endDrawingPointer,
		startDrawingTouch,
		drawTouch,
		endDrawingTouch,
		clearCanvas,
		saveCanvas,
		hasSignature,
	};
};
