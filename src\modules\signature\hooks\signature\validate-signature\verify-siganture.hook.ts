import { SignatureQueryKeys } from "@/modules/signature/query-keys";
import { validateAndMaskSignatureData } from "@/modules/signature/services/requests/signature/validate";
import { useQuery } from "@tanstack/react-query";

export const useValidateHashSignature = ({ hash }: { hash: string }) => {
	const { data, isLoading, error } = useQuery({
		queryKey: SignatureQueryKeys.validateSignatureVerification(hash),
		queryFn: () => validateAndMaskSignatureData({ signatureToken: hash }),
		enabled: !!hash,
	});

	return {
		data,
		isLoading,
		error,
	};
};
