import { useFindPersonByIdQuery } from "@/modules/presential/hooks/person/find-by-id.hook";
import { Skeleton } from "@/shared/components/ui/skeleton";

interface PersonCellProps {
	id: number;
}

export function PersonCell({ id }: PersonCellProps) {
	const { data, isLoading } = useFindPersonByIdQuery(id);

	return (
		<div className="flex items-center gap-3">
			{isLoading ? (
				<Skeleton className="h-4 w-24" />
			) : (
				<p className="font-medium text-gray-900">{data?.success ? data.data.name : `Pessoa #${id}`}</p>
			)}
		</div>
	);
}
