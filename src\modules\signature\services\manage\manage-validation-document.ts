import { IValidDocument } from "../../types/document/ validate-document.type";

export interface IDocumentValidationService {
	validate(file: File): Promise<IValidDocument>;
}

export class DocumentValidationService implements IDocumentValidationService {
	async validate(file: File): Promise<IValidDocument> {
		const formData = new FormData();
		formData.append("documento", file);

		const response = await fetch("/assinaturas/validar-documento/validar", {
			method: "POST",
			body: formData,
		});

		if (!response.ok) {
			throw new Error("Esse documento não é válido em nosso sistema.");
		}

		const data = await response.json();
		return data;
	}
}
