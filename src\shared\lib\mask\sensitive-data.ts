interface MaskOptions {
	maskChar?: string;
}

interface IMaskStrategy {
	canMask(input: string): boolean;
	mask(input: string, maskChar: string): string;
}

class DigitsUtil {
	public static removeNonDigits(str: string): string {
		return str.replace(/\D/g, "");
	}
}

class CPFMaskStrategy implements IMaskStrategy {
	public canMask(input: string): boolean {
		const digits = DigitsUtil.removeNonDigits(input);
		return digits.length === 11;
	}

	public mask(input: string, maskChar: string): string {
		const digits = DigitsUtil.removeNonDigits(input);
		return `${digits.slice(0, 3)}.${maskChar.repeat(3)}.${maskChar.repeat(3)}-${digits.slice(-2)}`;
	}
}

class CNPJMaskStrategy implements IMaskStrategy {
	public canMask(input: string): boolean {
		const digits = DigitsUtil.removeNonDigits(input);
		return digits.length === 14;
	}

	public mask(input: string, maskChar: string): string {
		const digits = DigitsUtil.removeNonDigits(input);
		return `${digits.slice(0, 2)}.${maskChar.repeat(3)}.${maskChar.repeat(3)}/${maskChar.repeat(4)}-${digits.slice(-2)}`;
	}
}

class PhoneMaskStrategy implements IMaskStrategy {
	private phoneRegex = /^\+?55?\(?(\d{2})\)?\s?(\d{4,5})-?(\d{4})$/;

	public canMask(input: string): boolean {
		return this.phoneRegex.test(input);
	}

	public mask(input: string, maskChar: string): string {
		const match = input.match(this.phoneRegex);
		if (match) {
			const [, area, firstPart, lastFour] = match;
			const maskedFirstPart = firstPart.replace(/\d/g, maskChar);
			return `(${area}) ${maskedFirstPart}-${lastFour}`;
		}
		return input;
	}
}

class EmailMaskStrategy implements IMaskStrategy {
	private emailRegex = /^([^@]+)@(.+)$/;

	public canMask(input: string): boolean {
		return this.emailRegex.test(input);
	}

	private maskMiddle(str: string, maskChar: string, unmaskedStart: number, unmaskedEnd: number): string {
		if (str.length <= unmaskedStart + unmaskedEnd) {
			return str
				.split("")
				.map((char, index) => (index < unmaskedStart || index >= str.length - unmaskedEnd ? char : maskChar))
				.join("");
		}
		const start = str.slice(0, unmaskedStart);
		const middle = maskChar.repeat(str.length - unmaskedStart - unmaskedEnd);
		const end = str.slice(-unmaskedEnd);
		return start + middle + end;
	}

	public mask(input: string, maskChar: string): string {
		const match = input.match(this.emailRegex);
		if (match) {
			const [, prefix, domain] = match;
			const maskedPrefix = this.maskMiddle(prefix, maskChar, 1, 1);
			return `${maskedPrefix}@${domain}`;
		}
		return input;
	}
}

class NameMaskStrategy implements IMaskStrategy {
	public canMask(_input: string): boolean {
		void _input;
		return true;
	}
	public mask(input: string, maskChar: string): string {
		const nameParts = input.split(" ");
		return nameParts
			.map(part => {
				if (part.length > 2) {
					return `${part[0]}${maskChar.repeat(part.length - 2)}${part.slice(-1)}`;
				} else if (part.length === 2) {
					return `${part[0]}${maskChar}`;
				} else {
					return `${part[0]}${maskChar}`;
				}
			})
			.join(" ");
	}
}

const strategies: IMaskStrategy[] = [
	new CPFMaskStrategy(),
	new CNPJMaskStrategy(),
	new PhoneMaskStrategy(),
	new EmailMaskStrategy(),
	new NameMaskStrategy(),
];

export const maskSensitiveData = (input: string, options: MaskOptions = {}): string => {
	const maskChar = options.maskChar || "*";

	const strategy = strategies.find(s => s.canMask(input));
	return strategy ? strategy.mask(input, maskChar) : input;
};
