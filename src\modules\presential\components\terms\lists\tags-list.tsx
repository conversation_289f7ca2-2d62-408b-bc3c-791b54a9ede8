import { useFindAllTagsQuery } from "@/modules/presential/hooks/terms/find-all-tags-query.hook";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Separator } from "@/shared/components/ui/separator";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { cn } from "@/shared/lib/utils";
import { FileText, PlusCircle, Tags } from "lucide-react";

const LoadingSkeletons = () => ["1", "2", "3"].map(id => <Skeleton key={id} className="h-12 w-56 rounded-xl" />);

const ErrorState = ({ message }: { message?: string }) => (
	<div className="text-center py-8 text-gray-500 w-full">
		<p>Erro ao carregar as tags</p>
		{message && <p className="text-sm">{message}</p>}
	</div>
);

const EmptyState = () => (
	<div className="text-center h-[45px] py-8 text-gray-500 w-full">
		<FileText className="mx-auto h-12 w-12 opacity-30" />
		<h3 className="mt-2 text-sm font-semibold">Nenhuma tag cadastrada</h3>
		<p className="mt-1 text-sm">Clique em Nova Tag para adicionar</p>
	</div>
);

const TagItem = ({ id, name }: { id: string; name: string }) => (
	<div className={cn("flex items-center h-[45px] gap-2 bg-gray-100 rounded-xl px-5 py-3 min-w-[220px]", "text-sm font-medium")}>
		<span>{name}</span>
		<span className="ml-auto flex items-center justify-center w-7 h-7 rounded bg-gray-200 text-gray-700 font-semibold text-xs">{id}</span>
	</div>
);

export const TagsList = ({ openTagModal }: { openTagModal?: () => void }) => {
	const { data: tagsData, isLoading } = useFindAllTagsQuery();

	let content;
	if (isLoading) content = <LoadingSkeletons />;
	else if (!tagsData?.success) content = <ErrorState message={tagsData?.data?.message} />;
	else if (tagsData.data.length === 0) content = <EmptyState />;
	else
		content = (
			<>
				{tagsData.data.map(tag => (
					<TagItem key={tag.id} id={String(tag.id)} name={tag.name} />
				))}
				<Button
					variant="outline"
					type="button"
					onClick={() => openTagModal?.()}
					className="flex items-center gap-2 h-[45px] border-dashed border border-gray-300 bg-transparent hover:bg-gray-50 rounded-xl px-5 py-3 min-w-[220px] text-gray-600"
				>
					<PlusCircle size={18} />
					Adicionar nova tag
				</Button>
			</>
		);

	return (
		<div className="flex flex-col w-full shadow rounded-lg overflow-hidden">
			<header className="flex p-4 items-center gap-3">
				<Tags className="w-6 h-6 text-primary" />
				<h2 className="text-lg font-semibold text-gray-900">Lista de Tags</h2>
			</header>
			<Separator className="w-full" />
			<main className={cn("flex gap-4 p-4 overflow-x-auto", "flex-wrap", "sm:flex-row", "flex-col space-y-1 sm:space-y-0 ")}>{content}</main>
		</div>
	);
};
