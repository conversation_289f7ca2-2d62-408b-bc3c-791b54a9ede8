import "./styles.css";

export const Loading: React.FC = () => {
	return (
		<div id="loading-page" className="flex fixed inset-0 sm:relative flex-col items-center justify-center">
			<div className="flex flex-col gap-4 w-full items-center justify-center">
				<div className="loader">
					{Array.from({ length: 6 }, (_, index) => (
						<span key={`loader-item-${index}`} />
					))}
				</div>
			</div>
			<h1 className="text-lg mt-10 font-bold text-gray-400">Buscando o documento...</h1>
		</div>
	);
};
