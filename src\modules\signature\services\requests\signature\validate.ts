"use server";

import { ISignatureTokenPayload } from "@/modules/signature/types/signature/sign-document.type";
import { IValidateSignature } from "@/modules/signature/types/signature/validate.type";
import { maskSensitiveData } from "@/shared/lib/mask/sensitive-data";
import { createRequest } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { SIGNATURE_ROUTES } from "../../endpoints";

const maskSignatureData = (data: IValidateSignature): IValidateSignature => {
	const { signatory, representative } = data;

	return {
		...data,
		signatory: {
			...signatory,
			cpf_cnpj: maskSensitiveData(signatory.cpf_cnpj),
			email: maskSensitiveData(signatory.email),
			phone: signatory.phone ? maskSensitiveData(signatory.phone) : undefined,
		},
		representative: representative
			? {
					...representative,
					cpf: maskSensitiveData(representative.cpf),
			  }
			: undefined,
	};
};

export const validateSignatureRequest = async ({ signatureToken }: ISignatureTokenPayload): Promise<ApiResponseReturn<IValidateSignature>> => {
	return createRequest<IValidateSignature>({
		method: "GET",
		path: SIGNATURE_ROUTES.VALIDATE_SIGNATURE({ signatureToken }),
	}) as Promise<ApiResponseReturn<IValidateSignature>>;
};

export const validateAndMaskSignatureData = async (payload: ISignatureTokenPayload): Promise<ApiResponseReturn<IValidateSignature>> => {
	const response = await validateSignatureRequest(payload);
	if (!response.success) return response;

	const maskedData = maskSignatureData(response.data);
	return { ...response, data: maskedData };
};
