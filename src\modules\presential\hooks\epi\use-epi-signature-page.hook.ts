import { useAtomValue, useSetAtom } from "jotai";
import {
	canProceedAtom,
	currentStepAtom,
	IEpiSignatureData,
	isLoadingAtom,
	nextStepAtom,
	previousStepAtom,
	resetProcessAtom,
	setStepAtom,
	signatureDataAtom,
	updateSignatureData<PERSON>tom,
} from "../../atoms/epi-signature.atoms";

export interface IHandleEpiSignaturePageHook {
	currentStep: number;
	isLoading: boolean;
	canProceed: boolean;
	signatureData: IEpiSignatureData;
	handleNextStep: () => void;
	handlePreviousStep: () => void;
	handleStepChange: (step: number) => void;
	updateSignatureData: (data: Partial<IEpiSignatureData>) => void;
	resetProcess: () => void;
	validateCurrentStep: () => boolean;
}

export const useEpiSignaturePage = (): IHandleEpiSignaturePageHook => {
	// Atoms values
	const currentStep = useAtomValue(currentStepAtom);
	const isLoading = useAtomValue(isLoadingAtom);
	const canProceed = useAtomValue(canProceedAtom);
	const signatureData = useAtomValue(signatureDataAtom);

	// Atoms actions
	const handleNextStep = useSetAtom(nextStepAtom);
	const handlePreviousStep = useSetAtom(previousStepAtom);
	const handleStepChange = useSetAtom(setStepAtom);
	const updateSignatureData = useSetAtom(updateSignatureDataAtom);
	const resetProcess = useSetAtom(resetProcessAtom);

	const validateCurrentStep = (): boolean => {
		return canProceed;
	};

	return {
		currentStep,
		isLoading,
		canProceed,
		signatureData,
		handleNextStep,
		handlePreviousStep,
		handleStepChange,
		updateSignatureData,
		resetProcess,
		validateCurrentStep,
	};
};
