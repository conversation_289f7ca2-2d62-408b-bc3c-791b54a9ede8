import { useAtom } from "jotai";
import { useEffect } from "react";
import { documentModalStateOpen } from "../../states/document/document-modal-open.state";

export const useDocumentModal = () => {
	const [isOpen, setIsOpen] = useAtom(documentModalStateOpen);

	useEffect(() => {
		if (isOpen) {
			document.body.style.overflow = "hidden";
		} else {
			document.body.style.overflow = "";
		}
		return () => {
			document.body.style.overflow = "";
		};
	}, [isOpen]);

	return {
		isOpen,
		setIsOpen,
	};
};
