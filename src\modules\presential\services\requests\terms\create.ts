"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { TERM_ENDPOINTS } from "../../endpoints";
import { ITerm } from "./find-all";

export interface ICreateTermBody {
	title: string;
	idTermTag: number;
	termo: File;
}

export const createTerm = async (body: ICreateTermBody): Promise<ApiResponseReturn<ITerm>> => {
	const formData = new FormData();
	formData.append("termo", body.termo);
	formData.append("title", body.title);
	formData.append("idTermTag", body.idTermTag.toString());

	return createRequestAdmin({
		method: "POST",
		path: TERM_ENDPOINTS.CREATE,
		body: formData,
	});
};
