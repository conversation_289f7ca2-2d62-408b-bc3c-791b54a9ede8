import {
	can<PERSON><PERSON>ceed<PERSON><PERSON>,
	currentStep<PERSON><PERSON>,
	isFirstStep<PERSON>tom,
	isLastStepAtom,
	isLoading<PERSON>tom,
	nextStep<PERSON>tom,
	previousStepAtom,
	progressAtom,
	resetProcess<PERSON>tom,
	setStep<PERSON>tom,
	switchToProviderMode<PERSON>tom,
	totalSteps<PERSON>tom,
	userR<PERSON><PERSON>tom,
} from "@/modules/presential/atoms/epi-signature.atoms";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { CheckCircle, ClipboardList, FileText, Shield, User } from "lucide-react";
import { useCallback } from "react";

export interface IStepConfig {
	id: number;
	title: string;
	description: string;
	icon: React.ElementType;
	mobileTitle?: string;
	isOptional?: boolean;
}

const PROVIDER_STEP_CONFIG: IStepConfig[] = [
	{ id: 1, title: "Termo", description: "Seleção de Termo", mobileTitle: "Termo", icon: FileText },
	{ id: 2, title: "EPIs", description: "Seleção de EPIs e quantidade", mobileTitle: "EPIs", icon: Shield },
];

const USER_STEP_CONFIG: IStepConfig[] = [
	{ id: 1, title: "Confirmar EPIs", description: "Confirmação dos EPIs selecionados", mobileTitle: "EPIs", icon: ClipboardList },
	{ id: 2, title: "Dados", description: "Preenchimento de dados pessoais", mobileTitle: "Dados", icon: User },
	{ id: 3, title: "Termo e Assinatura", description: "Leitura do termo e assinatura", mobileTitle: "Assinar", icon: FileText },
	{ id: 4, title: "Sucesso", description: "Processo concluído com sucesso", mobileTitle: "Sucesso", icon: CheckCircle },
];

export interface IHandleEpiSignatureStepperHook {
	currentStep: number;
	totalSteps: number;
	isLoading: boolean;
	canProceed: boolean;
	canGoBack: boolean;
	isFirstStep: boolean;
	isLastStep: boolean;
	progress: number;
	stepConfig: IStepConfig[];
	userRole: "provider" | "user";
	handleNextStep: () => void;
	handlePreviousStep: () => void;
	handleStepChange: (step: number) => void;
	handleReset: () => void;
	handleReturnToProvider: (keepEpis?: boolean) => void;
	getStepStatus: (stepId: number) => "pending" | "active" | "completed";
	getCurrentStepConfig: () => IStepConfig | undefined;
}

export const useEpiSignatureStepper = (): IHandleEpiSignatureStepperHook => {
	const currentStep = useAtomValue(currentStepAtom);
	const totalSteps = useAtomValue(totalStepsAtom);
	const isLoading = useAtomValue(isLoadingAtom);
	const canProceed = useAtomValue(canProceedAtom);
	const isFirstStep = useAtomValue(isFirstStepAtom);
	const isLastStep = useAtomValue(isLastStepAtom);
	const progress = useAtomValue(progressAtom);
	const userRole = useAtomValue(userRoleAtom);

	const nextStep = useSetAtom(nextStepAtom);
	const previousStep = useSetAtom(previousStepAtom);
	const setStep = useSetAtom(setStepAtom);
	const reset = useSetAtom(resetProcessAtom);
	const switchToProvider = useSetAtom(switchToProviderModeAtom);

	const canGoBack = !isFirstStep && !isLoading;
	const stepConfig = userRole === "provider" ? PROVIDER_STEP_CONFIG : USER_STEP_CONFIG;

	const handleNextStep = useCallback(() => {
		if (canProceed && !isLoading) nextStep();
	}, [canProceed, isLoading, nextStep]);

	const handlePreviousStep = useCallback(() => {
		if (canGoBack) previousStep();
	}, [canGoBack, previousStep]);

	const handleStepChange = useCallback(
		(step: number) => {
			if (step >= 1 && step <= totalSteps && !isLoading) setStep(step);
		},
		[totalSteps, isLoading, setStep]
	);

	const handleReset = useCallback(() => {
		if (!isLoading) reset();
	}, [isLoading, reset]);

	const handleReturnToProvider = useCallback(
		(keepEpis: boolean = false) => {
			if (!isLoading) switchToProvider(keepEpis);
		},
		[isLoading, switchToProvider]
	);

	const getStepStatus = useCallback(
		(stepId: number): "pending" | "active" | "completed" => (stepId < currentStep ? "completed" : stepId === currentStep ? "active" : "pending"),
		[currentStep]
	);

	const getCurrentStepConfig = useCallback(() => stepConfig.find(step => step.id === currentStep), [stepConfig, currentStep]);

	return {
		currentStep,
		totalSteps,
		isLoading,
		canProceed,
		canGoBack,
		isFirstStep,
		isLastStep,
		progress,
		stepConfig,
		userRole,
		handleNextStep,
		handlePreviousStep,
		handleStepChange,
		handleReset,
		handleReturnToProvider,
		getStepStatus,
		getCurrentStepConfig,
	};
};
