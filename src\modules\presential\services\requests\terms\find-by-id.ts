"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { TERM_ENDPOINTS } from "../../endpoints";
import { ITerm } from "./find-all";

export const findTermById = async (id: string | number): Promise<ApiResponseReturn<ITerm>> => {
	return createRequestAdmin({
		method: "GET",
		path: TERM_ENDPOINTS.GET_BY_ID(id),
	});
};
