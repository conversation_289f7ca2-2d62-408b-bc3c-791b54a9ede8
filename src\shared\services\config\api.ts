import { getCookie } from "@/shared/lib/cookies/get";
import axios from "axios";

export const apiInstance = axios.create({
	baseURL: process.env.API_URL,
});

export const apiInstanceAdmin = axios.create({
	baseURL: process.env.API_URL,
});

apiInstanceAdmin.interceptors.request.use(async config => {
	const token = await getCookie("admin-token");
	if (token.success) {
		config.headers.Authorization = `Bearer ${token.data.value}`;
	}
	return config;
});
