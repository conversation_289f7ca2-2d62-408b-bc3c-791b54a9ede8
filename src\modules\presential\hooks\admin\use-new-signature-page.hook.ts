"use client";

import { useRouter } from "next/navigation";
import { useCallback } from "react";

/**
 * Interface para definir as ações disponíveis na página de nova assinatura
 */
export interface IHandleNewSignaturePageActions {
	handleEpiSignatureClick(): void;
	handleOtherTypeClick(): void;
}

/**
 * Interface para definir o estado da página de nova assinatura
 */
export interface INewSignaturePageState {
	isOtherTypeDisabled: boolean;
}

/**
 * Interface completa do hook de nova assinatura
 */
export interface IUseNewSignaturePageHook extends IHandleNewSignaturePageActions, INewSignaturePageState {}

/**
 * Hook personalizado para gerenciar a lógica da página de seleção de tipo de assinatura.
 * 
 * Responsabilidades:
 * - Gerenciar navegação para diferentes tipos de assinatura
 * - Controlar estado de botões (habilitado/desabilitado)
 * - Separar lógica de negócio do componente JSX
 * 
 * Segue princípios SOLID:
 * - Single Responsibility: Apenas gerencia lógica da página de nova assinatura
 * - Open/Closed: Extensível para novos tipos de assinatura
 * - Interface Segregation: Interfaces específicas para ações e estado
 * 
 * @returns {IUseNewSignaturePageHook} Objeto com handlers e estado da página
 */
export const useNewSignaturePage = (): IUseNewSignaturePageHook => {
	const router = useRouter();

	/**
	 * Navega para a página de assinatura de EPI
	 */
	const handleEpiSignatureClick = useCallback(() => {
		router.push("/admin/epi/assinatura");
	}, [router]);

	/**
	 * Handler para o botão "Outro Tipo" (atualmente desabilitado)
	 * Pode ser expandido no futuro para incluir outros tipos de assinatura
	 */
	const handleOtherTypeClick = useCallback(() => {
		// Implementação futura para outros tipos de assinatura
		console.log("Outros tipos de assinatura estarão disponíveis em breve");
	}, []);

	/**
	 * Estado que indica se o botão "Outro Tipo" está desabilitado
	 * Atualmente sempre true, mas pode ser dinâmico no futuro
	 */
	const isOtherTypeDisabled = true;

	return {
		handleEpiSignatureClick,
		handleOtherTypeClick,
		isOtherTypeDisabled,
	};
};
