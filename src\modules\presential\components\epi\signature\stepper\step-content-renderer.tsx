import { EpiConfirmationStep } from "../steps/epi-confirmation-step";
import { EpiSelectionStep } from "../steps/epi-selection-step";
import { PersonalDataStep } from "../steps/personal-data-step";
import { SignatureStep } from "../steps/signature-step";
import { SuccessStep } from "../steps/success-step";
import { TermSelectionStep } from "../steps/term-selection";

interface IStepContentRendererProps {
	currentStep: number;
	userRole: "provider" | "user";
	onReturnToProvider: () => void;
}

export const StepContentRenderer = ({ currentStep, userRole, onReturnToProvider }: IStepContentRendererProps) => {
	if (userRole === "provider") {
		if (currentStep === 1) return <TermSelectionStep />;
		if (currentStep === 2) return <EpiSelectionStep />;
		return <TermSelectionStep />;
	}

	if (currentStep === 1) return <EpiConfirmationStep />;
	if (currentStep === 2) return <PersonalDataStep />;
	if (currentStep === 3) return <SignatureStep />;
	if (currentStep === 4) return <SuccessStep onReturnToProvider={onReturnToProvider} />;
	return <EpiConfirmationStep />;
};
