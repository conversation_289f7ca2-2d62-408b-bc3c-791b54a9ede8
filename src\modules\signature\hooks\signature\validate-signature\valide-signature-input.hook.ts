import { useRouter } from "next/navigation";

export interface ISignatureNavigator {
	navigateToValidate(hash: string): void;
}

export class SignatureNavigator implements ISignatureNavigator {
	constructor(private router: ReturnType<typeof useRouter>) {}

	navigateToValidate(hash: string): void {
		this.router.push(`/assinaturas/validar/${hash}`);
	}
}

export const isHashEmpty = (hash: string): boolean => hash.length === 0;

export const useSignatureValidation = (navigator?: ISignatureNavigator) => {
	const router = useRouter();
	const signatureNavigator = navigator || new SignatureNavigator(router);

	const onSubmit = (hash: string): void => {
		if (isHashEmpty(hash)) {
			return;
		}
		signatureNavigator.navigateToValidate(hash);
	};

	return {
		onSubmit,
		isHashEmpty,
	};
};
