import { But<PERSON> } from "@/shared/components/ui/button";
import { cn } from "@/shared/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface StepperFooterProps {
	handlePreviousStep: () => void;
	handleNextStep: () => void;
	handleReset: () => void;
	isLoading: boolean;
	isFirstStep: boolean;
	isLastStep: boolean;
	currentStep: number;
	totalSteps: number;
	progress: number;
	canProceed: boolean;
	userRole?: string;
}

export const StepperFooter = ({
	handlePreviousStep,
	handleNextStep,
	handleReset,
	isLoading,
	isFirstStep,
	isLastStep,
	currentStep,
	totalSteps,
	progress,
	canProceed,
	userRole = "provider",
}: StepperFooterProps) => (
	<footer className="flex flex-col p-4 md:p-6 bg-white shadow-lg rounded-lg border-t-4 border-pormade/20">
		{/* Mobile Progress */}
		<div className="mb-4 flex w-full justify-center items-center h-full sm:hidden">
			<div className="flex items-center w-full justify-between text-xs text-gray-500">
				<span>
					Etapa {currentStep} de {totalSteps}
				</span>
				<span>{Math.round(progress)}% concluído</span>
			</div>
		</div>
		{/* Actions */}
		<div className="flex flex-col sm:flex-row justify-between items-center gap-4">
			{/* Mobile Buttons */}
			<nav className="flex w-full sm:hidden justify-between items-center gap-2" aria-label="Stepper actions">
				<Button
					variant="ghost"
					size="icon"
					onClick={handlePreviousStep}
					disabled={isFirstStep || isLoading}
					className={cn("rounded-lg p-2 border border-gray-200 bg-white shadow-sm", "disabled:opacity-50 disabled:cursor-not-allowed")}
					aria-label="Anterior"
				>
					<ChevronLeft className="w-5 h-5" />
				</Button>
				<Button
					onClick={handleNextStep}
					disabled={!canProceed || isLoading}
					className={cn(
						"flex-1 h-11 px-4 rounded-lg bg-pormade hover:bg-pormade/90 text-white shadow-lg",
						"transition-all duration-200 hover:shadow-xl hover:scale-[1.02]",
						"disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
					)}
				>
					{isLoading ? (
						<>
							<svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
								<circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
								<path
									className="opacity-75"
									fill="currentColor"
									d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
								/>
							</svg>
							<span className="font-medium">Carregando...</span>
						</>
					) : (
						<>
							<span className="font-medium">Próximo</span>
							<ChevronRight className="w-4 h-4" />
						</>
					)}
				</Button>
				<Button
					variant="ghost"
					size="icon"
					onClick={handleReset}
					disabled={isLoading}
					className={cn(
						"rounded-lg p-2 border border-red-100 text-red-500 bg-white shadow-sm",
						"disabled:opacity-50 disabled:cursor-not-allowed"
					)}
					aria-label="Reiniciar"
				>
					<svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
						/>
					</svg>
				</Button>
			</nav>
			{/* Desktop Buttons */}
			<nav className="hidden sm:flex w-full justify-between items-center gap-4" aria-label="Stepper actions">
				<Button
					variant="outline"
					onClick={handlePreviousStep}
					disabled={isFirstStep || isLoading}
					className={cn(
						"w-full sm:w-auto flex items-center gap-2 h-11 px-6 border-gray-300",
						"hover:border-pormade hover:bg-pormade/5 transition-all duration-200",
						"disabled:opacity-50 disabled:cursor-not-allowed"
					)}
				>
					<ChevronLeft className="w-4 h-4" />
					<span className="font-medium">Anterior</span>
				</Button>
				<div className="flex items-center gap-2 sm:gap-3">
					<Button
						variant="outline"
						onClick={handleReset}
						disabled={isLoading}
						className={cn(
							"hidden sm:flex items-center gap-2 h-11 px-4 border-red-200 text-red-600",
							"hover:border-red-300 hover:bg-red-50 transition-all duration-200"
						)}
					>
						<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
							/>
						</svg>
						<span className="font-medium">Reiniciar</span>
					</Button>
					{(!isLastStep || (userRole === "provider" && isLastStep && canProceed)) && (
						<Button
							onClick={handleNextStep}
							disabled={!canProceed || isLoading}
							className={cn(
								"w-full sm:w-auto flex items-center gap-2 h-11 px-6 bg-pormade hover:bg-pormade/90 text-white shadow-lg",
								"transition-all duration-200 hover:shadow-xl hover:scale-[1.02]",
								"disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
							)}
						>
							{isLoading ? (
								<>
									<svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
										<circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
										<path
											className="opacity-75"
											fill="currentColor"
											d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
										/>
									</svg>
									<span className="font-medium">Carregando...</span>
								</>
							) : (
								<>
									<span className="font-medium">Próximo</span>
									<ChevronRight className="w-4 h-4" />
								</>
							)}
						</Button>
					)}
				</div>
			</nav>
		</div>
	</footer>
);
