import { useFindAllTagsQuery } from "@/modules/presential/hooks/terms/find-all-tags-query.hook";
import { useFindAllTermsQuery } from "@/modules/presential/hooks/terms/find-all-terms-query.hook";
import { ITerm } from "@/modules/presential/services/requests/terms/find-all";
import { Button } from "@/shared/components/ui/button";
import { Separator } from "@/shared/components/ui/separator";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/ui/table";
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { format } from "date-fns";
import { pt } from "date-fns/locale";
import { CalendarIcon, Edit, Eye, FileText, Tag } from "lucide-react";
import { useCallback, useMemo } from "react";
import { MobileTermCard } from "./mobile-term-card";

interface TermsListProps {
	onEditTerm: (term: ITerm) => void;
	onViewTerm: (term: ITerm) => void;
}

const COLUMN_WIDTHS = [420, 160, 200, 60];

export const TermsList = ({ onEditTerm, onViewTerm }: TermsListProps) => {
	const { data, isLoading } = useFindAllTermsQuery();
	const { data: tagsData, isLoading: isTagsLoading } = useFindAllTagsQuery();

	const getTagNameById = useCallback(
		(id: number) => (tagsData?.success ? tagsData.data.find(tag => tag.id === id)?.name : "Tag não encontrada"),
		[tagsData]
	);

	const formatDate = useCallback((dateString: string) => {
		try {
			return format(new Date(dateString), "dd 'de' MMMM 'de' yyyy 'às' HH:mm", { locale: pt });
		} catch {
			return dateString;
		}
	}, []);

	const columns = useMemo<ColumnDef<ITerm>[]>(
		() => [
			{
				header: "TÍTULO",
				accessorKey: "title",
				cell: ({ getValue }) => <span className="font-medium text-gray-900 truncate max-w-xs">{getValue() as string}</span>,
				size: COLUMN_WIDTHS[0],
			},
			{
				header: "TAG",
				accessorKey: "idTermTag",
				cell: ({ getValue }) => {
					const tagId = getValue() as number;
					return isTagsLoading ? (
						<Skeleton className="h-5 rounded-full" style={{ width: COLUMN_WIDTHS[1] - 32 }} />
					) : (
						<div className="inline-flex items-center gap-1 px-2 py-1 rounded-full bg-pormade/10 text-pormade text-xs font-medium">
							<Tag size={12} className="text-pormade" />
							{getTagNameById(tagId)}
						</div>
					);
				},
				size: COLUMN_WIDTHS[1],
			},
			{
				header: "DATA DE CRIAÇÃO",
				accessorKey: "createDate",
				cell: ({ getValue }) => (
					<span className="inline-flex items-center gap-1 text-gray-600">
						<CalendarIcon size={14} className="text-gray-400" />
						{formatDate(getValue() as string)}
					</span>
				),
				size: COLUMN_WIDTHS[2],
			},
			{
				header: "AÇÕES",
				id: "actions",
				cell: ({ row }) => (
					<div className="flex gap-1">
						<Button
							variant="ghost"
							size="sm"
							className="hover:bg-gray-200 h-8 w-8 p-0"
							onClick={() => onViewTerm(row.original)}
							aria-label="Visualizar termo"
							title="Visualizar termo"
						>
							<Eye size={16} className="text-gray-500" />
						</Button>
						<Button
							variant="ghost"
							size="sm"
							className="hover:bg-gray-200 h-8 w-8 p-0"
							onClick={() => onEditTerm(row.original)}
							aria-label="Editar termo"
							title="Editar termo"
						>
							<Edit size={16} className="text-gray-500" />
						</Button>
					</div>
				),
				size: COLUMN_WIDTHS[3],
			},
		],
		[isTagsLoading, getTagNameById, formatDate, onEditTerm, onViewTerm]
	);

	const table = useReactTable({
		data: Array.isArray(data?.data) ? data.data : [],
		columns,
		getCoreRowModel: getCoreRowModel(),
	});

	const renderTableContent = () => {
		if (isLoading) {
			return (
				<TableBody className="bg-white px-15">
					{[...Array(2)].map((_, idx) => (
						<TableRow key={idx}>
							{COLUMN_WIDTHS.map((width, colIdx) => (
								<TableCell key={colIdx} className="px-4 py-4" style={{ width }}>
									<Skeleton className="h-5" style={{ width: width - 32 }} />
								</TableCell>
							))}
						</TableRow>
					))}
				</TableBody>
			);
		}

		if (!data?.success) {
			return (
				<TableBody>
					<TableRow>
						<TableCell colSpan={4} className="h-32 text-center">
							<p className="text-gray-500">Erro ao carregar os termos</p>
							<p className="text-sm text-gray-500">{data?.data?.message}</p>
						</TableCell>
					</TableRow>
				</TableBody>
			);
		}

		if (data.data.length === 0) {
			return (
				<TableBody>
					<TableRow>
						<TableCell colSpan={4} className="h-32 text-center">
							<FileText className="mx-auto h-12 w-12 opacity-30" />
							<h3 className="mt-2 text-lg font-semibold text-gray-500">Nenhum termo cadastrado</h3>
							<p className="mt-1 text-sm text-gray-500">
								Clique em <span className="font-medium text-primary">&quot;Novo Termo&quot;</span> para adicionar
							</p>
						</TableCell>
					</TableRow>
				</TableBody>
			);
		}

		return (
			<TableBody className="bg-white px-15">
				{table.getRowModel().rows.map(row => (
					<TableRow key={row.id} className="hover:bg-gray-100/40 transition-colors">
						{row.getVisibleCells().map((cell, idx) => (
							<TableCell key={cell.id} className="px-4 py-4" style={{ width: COLUMN_WIDTHS[idx] }}>
								{flexRender(cell.column.columnDef.cell, cell.getContext())}
							</TableCell>
						))}
					</TableRow>
				))}
			</TableBody>
		);
	};

	const renderMobileContent = () => {
		if (isLoading) {
			return (
				<div className="space-y-4 px-4 py-2">
					{[...Array(3)].map((_, idx) => (
						<Skeleton key={idx} className="h-20 w-full rounded-lg" />
					))}
				</div>
			);
		}
		if (!data?.success) {
			return (
				<div className="text-center py-8 text-gray-500 px-4">
					<p>Erro ao carregar os termos</p>
					<p className="text-sm">{data?.data?.message}</p>
				</div>
			);
		}
		if (data.data.length === 0) {
			return (
				<div className="text-center py-8 text-gray-500 px-4">
					<FileText className="mx-auto h-12 w-12 opacity-30" />
					<h3 className="mt-2 text-lg font-semibold">Nenhum termo cadastrado</h3>
					<p className="mt-1 text-sm">
						Clique em <span className="font-medium text-primary">&quot;Novo Termo&quot;</span> para adicionar
					</p>
				</div>
			);
		}
		return (
			<div className="space-y-4 px-2 py-2">
				{data.data.map(term => (
					<MobileTermCard
						key={term.id}
						term={term}
						onEdit={onEditTerm}
						onView={onViewTerm}
						getTagName={getTagNameById}
						formatDate={formatDate}
						isTagsLoading={isTagsLoading}
					/>
				))}
			</div>
		);
	};

	return (
		<div className="flex flex-col max-w-full w-full shadow rounded-lg overflow-hidden">
			<header className="flex p-4 items-center gap-3">
				<FileText className="w-6 h-6 text-primary" />
				<h2 className="text-lg font-semibold text-gray-900">Lista de Termos</h2>
			</header>
			<Separator className="w-full" />
			<div className="lg:hidden">{renderMobileContent()}</div>
			<main className="bg-gray-100 overflow-x-auto hidden lg:block">
				<Table>
					<TableHeader className="px-15">
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map((header, idx) => (
									<TableHead
										key={header.id}
										className="px-4 py-3 text-sm text-gray-500 font-normal text-left"
										style={{ width: COLUMN_WIDTHS[idx] }}
									>
										{flexRender(header.column.columnDef.header, header.getContext())}
									</TableHead>
								))}
							</TableRow>
						))}
					</TableHeader>
					{renderTableContent()}
				</Table>
			</main>
		</div>
	);
};
