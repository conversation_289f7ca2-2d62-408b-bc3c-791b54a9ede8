"use server";
import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { EPI_ENDPOINTS } from "../../endpoints";

export interface IEpiReturn {
	id: number;
	signatureDate: string;
	isSigned: boolean;
	idTerm: number;
	idSignatureKey: number;
	idWithdrawalEpiGroup: number;
	idPerson: number;
}

export interface FindAllEpiReturnParams {
	personId?: number;
	signed?: boolean;
}

export const findAllSignEpiReturnRequest = async (params: FindAllEpiReturnParams): Promise<ApiResponseReturn<IEpiReturn[]>> => {
	return createRequestAdmin({
		method: "GET",
		path: EPI_ENDPOINTS.GET_ALL_EPI_RETURN({
			personId: params.personId,
			signed: params.signed,
		}),
	});
};
