import { SignatureOverlayRenderer } from '../signature-overlay-renderer';
import { ISignatureOverlay, ISignatureRenderData } from '../../../types/signature/signature-overlay.interface';

// Mock do HTMLImageElement
class MockImage {
  src = '';
  naturalWidth = 100;
  naturalHeight = 50;
  onload: (() => void) | null = null;
  onerror: ((error: any) => void) | null = null;

  constructor() {
    // Simula carregamento assíncrono
    setTimeout(() => {
      if (this.onload) {
        this.onload();
      }
    }, 0);
  }
}

// Mock do CanvasRenderingContext2D
class MockCanvasContext {
  fillStyle = '';
  strokeStyle = '';
  lineWidth = 0;
  font = '';
  textAlign = '';
  textBaseline = '';
  shadowColor = '';
  shadowBlur = 0;
  shadowOffsetX = 0;
  shadowOffsetY = 0;

  save = jest.fn();
  restore = jest.fn();
  beginPath = jest.fn();
  moveTo = jest.fn();
  lineTo = jest.fn();
  quadraticCurveTo = jest.fn();
  closePath = jest.fn();
  stroke = jest.fn();
  fill = jest.fn();
  drawImage = jest.fn();
  setLineDash = jest.fn();
  createLinearGradient = jest.fn(() => ({
    addColorStop: jest.fn(),
  }));
  measureText = jest.fn(() => ({
    width: 50,
  }));
  fillText = jest.fn();
}

// Setup dos mocks globais
beforeAll(() => {
  global.Image = MockImage as any;
  global.btoa = jest.fn((str) => Buffer.from(str).toString('base64'));
});

describe('SignatureOverlayRenderer', () => {
  let renderer: SignatureOverlayRenderer;
  let mockContext: MockCanvasContext;

  beforeEach(() => {
    renderer = new SignatureOverlayRenderer();
    mockContext = new MockCanvasContext();
  });

  it('deve ter o tipo correto', () => {
    expect(renderer.supportedType).toBe('signature');
  });

  it('deve renderizar assinatura básica', async () => {
    const overlay: ISignatureOverlay = {
      id: 'test-signature',
      type: 'signature',
      page: 0,
      x: 100,
      y: 200,
      scale: 1,
      svgData: '<svg>test</svg>',
    };

    await renderer.render(mockContext as any, overlay, 1);

    expect(mockContext.drawImage).toHaveBeenCalled();
    expect(mockContext.save).toHaveBeenCalled();
    expect(mockContext.restore).toHaveBeenCalled();
  });

  it('deve renderizar prévia com borda', async () => {
    const overlay: ISignatureOverlay = {
      id: 'test-signature',
      type: 'signature',
      page: 0,
      x: 100,
      y: 200,
      scale: 1,
      svgData: '<svg>test</svg>',
      isPreview: true,
    };

    await renderer.render(mockContext as any, overlay, 1);

    expect(mockContext.drawImage).toHaveBeenCalled();
    expect(mockContext.setLineDash).toHaveBeenCalledWith([6, 4]);
    expect(mockContext.stroke).toHaveBeenCalled();
    expect(mockContext.fillText).toHaveBeenCalled();
  });

  it('deve usar dados de renderização customizados', async () => {
    const overlay: ISignatureOverlay = {
      id: 'test-signature',
      type: 'signature',
      page: 0,
      x: 100,
      y: 200,
      scale: 1,
      svgData: '<svg>test</svg>',
    };

    const renderData: ISignatureRenderData = {
      svg: '<svg>custom</svg>',
      showPreview: true,
      previewText: 'CUSTOM',
      previewBorderColor: '#ff0000',
    };

    await renderer.render(mockContext as any, overlay, 1, renderData);

    expect(mockContext.drawImage).toHaveBeenCalled();
    expect(mockContext.fillText).toHaveBeenCalledWith('CUSTOM', expect.any(Number), expect.any(Number));
  });

  it('deve lidar com erro de carregamento de SVG', async () => {
    // Mock console.error para verificar se foi chamado
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    // Mock Image que falha no carregamento
    const FailingImage = class {
      onerror: ((error: any) => void) | null = null;
      
      constructor() {
        setTimeout(() => {
          if (this.onerror) {
            this.onerror(new Error('Failed to load'));
          }
        }, 0);
      }
    };

    global.Image = FailingImage as any;

    const overlay: ISignatureOverlay = {
      id: 'test-signature',
      type: 'signature',
      page: 0,
      x: 100,
      y: 200,
      scale: 1,
      svgData: '<svg>test</svg>',
    };

    await renderer.render(mockContext as any, overlay, 1);

    expect(consoleSpy).toHaveBeenCalledWith('Erro ao renderizar assinatura:', expect.any(Error));
    
    consoleSpy.mockRestore();
    global.Image = MockImage as any; // Restaura o mock original
  });

  it('deve avisar quando SVG não está disponível', async () => {
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

    const overlay: ISignatureOverlay = {
      id: 'test-signature',
      type: 'signature',
      page: 0,
      x: 100,
      y: 200,
      scale: 1,
      svgData: '',
    };

    await renderer.render(mockContext as any, overlay, 1);

    expect(consoleSpy).toHaveBeenCalledWith('SVG data não encontrado para assinatura', 'test-signature');
    expect(mockContext.drawImage).not.toHaveBeenCalled();
    
    consoleSpy.mockRestore();
  });
});
