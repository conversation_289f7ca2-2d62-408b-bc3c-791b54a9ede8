"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { TERM_ENDPOINTS } from "../../endpoints";

export interface ICreateTagBody {
	name: string;
}

export interface ITermTag {
	id: number;
	name: string;
}

export const createTermTag = async (body: ICreateTagBody): Promise<ApiResponseReturn<ITermTag>> => {
	return createRequestAdmin({
		method: "POST",
		path: TERM_ENDPOINTS.CREATE_TAG,
		body,
	});
};
