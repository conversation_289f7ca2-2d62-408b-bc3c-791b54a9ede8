import { useCallback } from "react";

export interface IScrollPosition {
	x: number;
	y: number;
	page: number;
}

export function useScrollToPosition(containerRef: React.RefObject<HTMLElement | null>, zoom: number) {
	return useCallback(
		(position: IScrollPosition | null) => {
			if (!position) return;
			const container = containerRef.current;
			if (!container) return;

			const canvas = container.querySelector<HTMLCanvasElement>(`canvas[data-page="${position.page + 1}"]`);
			if (!canvas) return;

			const targetX = canvas.offsetLeft + position.x * zoom;
			const targetY = canvas.offsetTop + position.y * zoom;
			const { width, height } = container.getBoundingClientRect();

			container.scrollTo({
				left: targetX - width / 2,
				top: targetY - height / 2,
				behavior: "smooth",
			});
		},
		[containerRef, zoom]
	);
}
