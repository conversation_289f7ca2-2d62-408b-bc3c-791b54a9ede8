import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/shared/components/ui/card";
import { Shield, User } from "lucide-react";
import { useAtomValue } from "jotai";
import { signatureDataAtom } from "@/modules/presential/atoms/epi-signature.atoms";
import { Badge } from "@/shared/components/ui/badge";

export const EpiConfirmationStep = () => {
	const signatureData = useAtomValue(signatureDataAtom);

	type EpiItem = {
		name?: string;
		description?: string;
		quantity?: number;
	};

	const selectedEpis = (signatureData.selectedEpis as EpiItem[]) || [];
	const signatoryCount = signatureData.signatoryCount || 0;

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="text-center space-y-2">
				<div className="w-12 h-12 bg-pormade/10 rounded-full flex items-center justify-center mx-auto">
					<Shield className="w-6 h-6 text-pormade" />
				</div>
				<h1 className="text-2xl font-bold text-gray-900">Confirmar EPIs</h1>
				<p className="text-sm text-gray-600 max-w-md mx-auto">Verifique os EPIs selecionados pelo responsável e confirme para prosseguir.</p>
			</div>

			{/* EPIs List */}
			<Card>
				<CardHeader className="pb-4">
					<CardTitle className="flex items-center gap-2 text-pormade">
						<Shield className="w-5 h-5" />
						EPIs Selecionados
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					{selectedEpis.length > 0 ? (
						<>
							{/* EPIs */}
							<div className="space-y-3">
								{selectedEpis.map((epi: EpiItem, index: number) => (
									<div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border">
										<div className="flex items-center gap-3">
											<div className="w-8 h-8 bg-pormade/10 rounded-lg flex items-center justify-center">
												<Shield className="w-4 h-4 text-pormade" />
											</div>
											<div>
												<p className="font-medium text-gray-900">{epi.name || "EPI"}</p>
												{epi.description && <p className="text-xs text-gray-500">{epi.description}</p>}
											</div>
										</div>
										<Badge variant="secondary" className="bg-pormade/10 text-pormade">
											{epi.quantity || 1}x
										</Badge>
									</div>
								))}
							</div>

							{/* Signatory Count */}
							<div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
								<div className="flex items-center gap-3">
									<div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
										<User className="w-4 h-4 text-white" />
									</div>
									<div className="flex-1">
										<p className="font-medium text-blue-900">Quantidade de Assinantes</p>
										<p className="text-sm text-blue-700">
											{signatoryCount} {signatoryCount === 1 ? "pessoa irá" : "pessoas irão"} assinar
										</p>
									</div>
									<Badge className="bg-blue-600 text-white font-bold">{signatoryCount}</Badge>
								</div>
							</div>
						</>
					) : (
						<div className="text-center py-8">
							<div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
								<Shield className="w-8 h-8 text-gray-300" />
							</div>
							<p className="font-medium text-gray-500">Nenhum EPI foi selecionado</p>
							<p className="text-sm text-gray-400">Selecione os EPIs necessários para continuar</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Info */}
			<div className="text-center">
				<p className="text-sm text-gray-500 max-w-sm mx-auto">
					Ao clicar em próximo, você prosseguirá para o preenchimento dos seus dados pessoais.
				</p>
			</div>
		</div>
	);
};
