import { useAtomValue } from "jotai";
import { useFormContext } from "react-hook-form";
import { rubricSvgString } from "../../../states/rubric/rubric-svg.state";
import { mainRequisitToSignAtom } from "../../../states/signature/main-requisit-to-sign.state";
import { signaturePositionAtom } from "../../../states/signature/signature-position.state";

export const useSignatureActions = (onSubmit: () => void) => {
	const minRequirements = useAtomValue(mainRequisitToSignAtom);
	const { watch, handleSubmit } = useFormContext();

	const terms1 = watch("terms1");
	const cpf = watch("cpf");

	const hasAcceptedTerms = Boolean(terms1);
	const isCpfValid = Boolean(cpf) && cpf.length >= 11;
	const hasMinRequirements = Boolean(minRequirements);
	const isFormValid = hasAcceptedTerms && isCpfValid && hasMinRequirements;

	const isDisabled = !isFormValid;
	const rubricSvg = useAtomValue(rubricSvgString);
	const signaturePosition = useAtomValue(signaturePositionAtom);
	const isSignaturePositionDisabled = !signaturePosition && rubricSvg !== null;

	return {
		submitFormHandler: handleSubmit(onSubmit),
		isDisabled,
		rubricSvg,
		signaturePosition,
		isSignaturePositionDisabled,
	};
};
