import React, { memo, useMemo, useState, useEffect } from "react";
import { IPdfOverlay } from "../types/pdf-overlay.interface";
import PdfCanvas from "./pdf-canvas";

// Tipo importado dinamicamente para evitar problemas de SSR
import type { PDFDocumentProxy } from "pdfjs-dist";

interface PdfViewerContentProps {
	pdfDocument: PDFDocumentProxy;
	totalPages: number;
	zoom: number;
	onRenderComplete: (pageNumber: number) => void;
	forceRenderAllPages: boolean;
	overlays?: IPdfOverlay[];
	overlayData?: Record<string, unknown>;
	onRenderOverlays?: (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => Promise<void>;
}

const PdfViewerContent: React.FC<PdfViewerContentProps> = memo(
	({ pdfDocument, totalPages, zoom, onRenderComplete, forceRenderAllPages, overlays, overlayData, onRenderOverlays }) => {
		const pageNumbers = useMemo(() => Array.from({ length: totalPages }, (_, index) => index + 1), [totalPages]);
		const [isContentReady, setIsContentReady] = useState(false);

		useEffect(() => {
			const timer = setTimeout(() => {
				setIsContentReady(true);
			}, 100);
			return () => clearTimeout(timer);
		}, [pdfDocument]);

		return (
			<div
				style={{
					opacity: isContentReady ? 1 : 0,
					transition: "opacity 0.5s ease-in-out",
					minHeight: "100px",
				}}
			>
				{pageNumbers.map(pageNumber => (
					<PdfCanvas
						key={`pdf-page-${pageNumber}`}
						pdfDocument={pdfDocument}
						pageNumber={pageNumber}
						zoom={zoom}
						onRenderComplete={() => onRenderComplete(pageNumber)}
						forceRenderAllPages={forceRenderAllPages}
						overlays={overlays}
						overlayData={overlayData}
						onRenderOverlays={onRenderOverlays}
					/>
				))}
			</div>
		);
	}
);

PdfViewerContent.displayName = "PdfViewerContent";

export default PdfViewerContent;
