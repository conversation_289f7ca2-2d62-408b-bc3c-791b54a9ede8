import { atom } from "jotai";

export type UserRole = "provider" | "user";

export interface IEpiSignatureData {
	selectedPersonId?: number;
	selectedTermId?: number;
	selectedEpiGroupId?: number;
	selectedEpis?: unknown[];
	epiItems?: unknown[];
	personCpf?: string;
	personName?: string;
	photo?: File;
	signatoryCount?: number;
	signature?: string;
	termReadCompleted?: boolean;
	availableTerms?: { id: number; [key: string]: unknown }[];
	previousEpis?: unknown[]; // Para reutilização de EPIs
}

export const currentStepAtom = atom<number>(1);
export const isLoadingAtom = atom<boolean>(false);
export const userRoleAtom = atom<UserRole>("provider");
export const signatureDataAtom = atom<IEpiSignatureData>({});

export const canProceedAtom = atom(get => {
	const currentStep = get(currentStepAtom);
	const userRole = get(userRoleAtom);
	const signatureData = get(signatureDataAtom);

	if (userRole === "provider") {
		switch (currentStep) {
			case 1:
				// Validar se termo foi selecionado (ou auto-selecionado)
				return !!signatureData.selectedTermId;
			case 2:
				// Validar se EPIs foram selecionados e quantidade definida
				return !!(signatureData.selectedEpis?.length && signatureData.signatoryCount);
			default:
				return false;
		}
	} else {
		// userRole === "user"
		switch (currentStep) {
			case 1:
				// Etapa de confirmação dos EPIs - sempre válida se chegou até aqui
				return true;
			case 2:
				// Validar se CPF, nome e foto foram preenchidos
				return !!(signatureData.personCpf && signatureData.personName && signatureData.photo);
			case 3:
				// Validar se leitura do termo foi concluída e assinatura foi realizada
				return !!(signatureData.termReadCompleted && signatureData.signature);
			case 4:
				// Etapa de sucesso - sempre válida se chegou até aqui
				return true;
			default:
				return false;
		}
	}
});

// Atoms derivados para controle do stepper
export const totalStepsAtom = atom<number>(get => {
	const userRole = get(userRoleAtom);
	return userRole === "provider" ? 2 : 4;
});

export const isFirstStepAtom = atom(get => get(currentStepAtom) === 1);
export const isLastStepAtom = atom(get => get(currentStepAtom) === get(totalStepsAtom));
export const progressAtom = atom(get => (get(currentStepAtom) / get(totalStepsAtom)) * 100);

// Actions atoms
export const nextStepAtom = atom(null, (get, set) => {
	const currentStep = get(currentStepAtom);
	const canProceed = get(canProceedAtom);
	const totalSteps = get(totalStepsAtom);
	const userRole = get(userRoleAtom);

	if (canProceed && currentStep < totalSteps) {
		set(currentStepAtom, currentStep + 1);
	} else if (userRole === "provider" && currentStep === totalSteps && canProceed) {
		// Transição de provider para user
		set(userRoleAtom, "user");
		set(currentStepAtom, 1);
	}
});

export const switchToUserModeAtom = atom(null, (_get, set) => {
	set(userRoleAtom, "user");
	set(currentStepAtom, 1);
});

export const switchToProviderModeAtom = atom(null, (get, set, keepEpis: boolean = false) => {
	const currentData = get(signatureDataAtom);

	// Salvar EPIs para reutilização se solicitado
	const dataToKeep = keepEpis
		? {
				selectedTermId: currentData.selectedTermId,
				selectedEpiGroupId: currentData.selectedEpiGroupId,
				selectedEpis: currentData.selectedEpis,
				epiItems: currentData.epiItems,
				signatoryCount: currentData.signatoryCount,
				previousEpis: currentData.selectedEpis,
				availableTerms: currentData.availableTerms,
		  }
		: {
				availableTerms: currentData.availableTerms,
				previousEpis: currentData.selectedEpis,
		  };

	set(userRoleAtom, "provider");
	set(currentStepAtom, keepEpis ? 2 : 1);
	set(signatureDataAtom, dataToKeep);
});

export const autoSelectTermAtom = atom(null, (get, set) => {
	const signatureData = get(signatureDataAtom);
	const availableTerms = signatureData.availableTerms || [];

	if (availableTerms.length === 1) {
		set(updateSignatureDataAtom, { selectedTermId: availableTerms[0].id });
		// Auto avançar para próxima etapa se estiver na etapa 1
		const currentStep = get(currentStepAtom);
		if (currentStep === 1) {
			set(currentStepAtom, 2);
		}
	}
});

export const previousStepAtom = atom(null, (get, set) => {
	const currentStep = get(currentStepAtom);
	if (currentStep > 1) {
		set(currentStepAtom, currentStep - 1);
	}
});

export const setStepAtom = atom(null, (get, set, step: number) => {
	const totalSteps = get(totalStepsAtom);
	if (step >= 1 && step <= totalSteps) {
		set(currentStepAtom, step);
	}
});

export const updateSignatureDataAtom = atom(null, (get, set, data: Partial<IEpiSignatureData>) => {
	const currentData = get(signatureDataAtom);
	set(signatureDataAtom, { ...currentData, ...data });
});

export const resetProcessAtom = atom(null, (_get, set) => {
	set(currentStepAtom, 1);
	set(signatureDataAtom, {});
	set(isLoadingAtom, false);
});
