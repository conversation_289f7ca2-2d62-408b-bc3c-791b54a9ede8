"use client";

import { AdminHeader } from "@/modules/presential/components/admin/admin-header";
import { IUser } from "@/modules/presential/types/user";
import { removeCookie } from "@/shared/lib/cookies/remove";
import { useRouter } from "next/navigation";

const mockUser: IUser = {
	id: "1",
	name: "<PERSON>",
	email: "<EMAIL>",
	role: "admin",
};

export default function AdminLayout({ children }: { children: React.ReactNode }) {
	const router = useRouter();

	const handleLogout = async () => {
		const data = await removeCookie("admin-token");
		if (data.success) router.replace("/admin");
	};

	return (
		<div className="min-h-screen bg-gray-50">
			<AdminHeader user={mockUser} onLogout={handleLogout} />
			<main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">{children}</main>
		</div>
	);
}
