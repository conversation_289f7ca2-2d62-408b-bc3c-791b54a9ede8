import { IAdminLoginFormSchema } from "@/modules/presential/validators/admin-login.form";
import { Control, Controller, FieldErrors, UseFormHandleSubmit } from "react-hook-form";
import { FiLock, FiUser } from "react-icons/fi";

interface LoginFormProps {
	control: Control<IAdminLoginFormSchema>;
	handleSubmit: UseFormHandleSubmit<IAdminLoginFormSchema>;
	errors: FieldErrors<IAdminLoginFormSchema>;
	onSubmit: (data: IAdminLoginFormSchema) => Promise<void>;
	isLoading: boolean;
}

export const LoginForm = ({ control, handleSubmit, errors, onSubmit, isLoading }: LoginFormProps) => {
	return (
		<form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
			<div className="space-y-5">
				<div>
					<label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1.5">
						Usuário
					</label>
					<div className="relative">
						<div className="absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none">
							<FiUser className="h-5 w-5 text-gray-400" />
						</div>
						<Controller
							name="username"
							control={control}
							render={({ field }) => (
								<input
									{...field}
									id="username"
									type="text"
									className="appearance-none block w-full pl-11 pr-3 py-3.5 border border-gray-300 rounded-xl
										focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 
										text-gray-900 placeholder-gray-400 bg-white shadow-sm
										transition duration-150 ease-in-out text-base"
									placeholder="Digite seu usuário"
								/>
							)}
						/>
					</div>
					{errors.username && <p className="text-red-500">{errors.username.message}</p>}
				</div>

				<div>
					<label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1.5">
						Senha
					</label>
					<div className="relative">
						<div className="absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none">
							<FiLock className="h-5 w-5 text-gray-400" />
						</div>
						<Controller
							name="password"
							control={control}
							render={({ field }) => (
								<input
									{...field}
									type="password"
									className="appearance-none block w-full pl-11 pr-3 py-3.5 border border-gray-300 rounded-xl
										focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 
										text-gray-900 placeholder-gray-400 bg-white shadow-sm
										transition duration-150 ease-in-out text-base"
									placeholder="••••••••"
								/>
							)}
						/>
					</div>
					{errors.password && <p className="text-red-500">{errors.password.message}</p>}
				</div>
			</div>

			<div className="flex items-center justify-between">
				<div className="flex items-center">
					<Controller
						name="signIn"
						control={control}
						render={({ field: { onChange, value, ...field } }) => (
							<>
								<input
									{...field}
									type="checkbox"
									id="remember-me"
									onChange={e => onChange(e.target.checked)}
									checked={value || false}
									className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded
										transition duration-150 ease-in-out"
								/>
								<label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
									Lembrar-me
								</label>
							</>
						)}
					/>
				</div>

				<div className="text-sm">
					<button
						type="button"
						onClick={() => {
							/* implementar recuperação de senha */
						}}
						className="font-medium text-gray-600 hover:text-gray-800 
							transition duration-150 ease-in-out"
					>
						Esqueceu sua senha?
					</button>
				</div>
			</div>

			<div className="pt-2">
				<button
					type="submit"
					disabled={isLoading}
					className="w-full flex justify-center py-4 px-4 border border-transparent rounded-xl
						text-base font-medium text-white bg-gray-800 hover:bg-gray-900 
						focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 
						shadow-md active:transform active:scale-[0.98] transition duration-150 ease-in-out"
				>
					{isLoading ? "Aguarde..." : "Entrar no sistema"}
				</button>
			</div>
		</form>
	);
};
