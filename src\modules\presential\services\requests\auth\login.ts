"use server";

import { createCookie } from "@/shared/lib/cookies/create";
import { createRequest } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { ILoginDTO } from "../../../dto/login.dto";
import { ADMIN_AUTH_ENDPOINTS } from "../../endpoints";

const SEVEN_DAYS_IN_SECONDS = 7 * 24 * 60 * 60;

export const loginRequest = async (user: ILoginDTO, signIn: boolean): Promise<ApiResponseReturn<boolean>> => {
	const response = await createRequest<{ access_token: string }>({
		method: "POST",
		path: ADMIN_AUTH_ENDPOINTS.LOGIN,
		body: user,
	});
	if (!response.success) return { ...response, data: response.data };
	const cookieOptions = signIn ? { maxAge: SEVEN_DAYS_IN_SECONDS } : undefined;
	const saveCookie = await createCookie({
		value: response.data.access_token,
		name: "admin-token",
		options: cookieOptions,
	});
	if (!saveCookie.success) return { success: false, data: saveCookie.data, status: response.status };
	return { success: true, data: true, status: response.status };
};
