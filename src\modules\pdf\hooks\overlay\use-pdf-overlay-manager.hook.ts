import { useAtom, useAtomValue } from "jotai";
import { useCallback, useMemo } from "react";
import { pdfOverlaysAtom, pdfRenderersAtom } from "../../states";
import { globalRenderersAtom } from "../../states/global-renderers.state";
import { IPdfOverlay, IPdfOverlayRenderer } from "../../types/pdf-overlay.interface";

type OverlayManager = {
	addOverlay: (overlay: IPdfOverlay) => void;
	removeOverlay: (id: string) => void;
	getOverlaysForPage: (page: number) => IPdfOverlay[];
	registerRenderer: (renderer: IPdfOverlayRenderer) => void;
	unregisterRenderer: (type: string) => void;
	getRenderer: (type: string) => IPdfOverlayRenderer | undefined;
};

export const usePdfOverlayManager = (): OverlayManager => {
	const [overlays, setOverlays] = useAtom(pdfOverlaysAtom);
	const [renderers, setRenderers] = useAtom(pdfRenderersAtom);
	const globalRenderers = useAtomValue(globalRenderersAtom);

	const addOverlay = useCallback(
		(overlay: IPdfOverlay) => {
			setOverlays(current => {
				const updated = new Map(current);
				const pageOverlays = [...(updated.get(overlay.page) || [])];
				const idx = pageOverlays.findIndex(o => o.id === overlay.id);
				if (idx >= 0) pageOverlays[idx] = overlay;
				else pageOverlays.push(overlay);
				updated.set(overlay.page, pageOverlays);
				return updated;
			});
		},
		[setOverlays]
	);

	const removeOverlay = useCallback(
		(id: string) => {
			setOverlays(current => {
				const updated = new Map(current);
				let changed = false;
				updated.forEach((pageOverlays, page) => {
					const filtered = pageOverlays.filter(o => o.id !== id);
					if (filtered.length !== pageOverlays.length) {
						updated.set(page, filtered);
						changed = true;
					}
				});
				return changed ? updated : current;
			});
		},
		[setOverlays]
	);

	const getOverlaysForPage = useCallback((page: number) => overlays.get(page) || [], [overlays]);

	const registerRenderer = useCallback(
		(renderer: IPdfOverlayRenderer) => {
			setRenderers(current => {
				const updated = new Map(current);
				updated.set(renderer.supportedType, renderer);
				return updated;
			});
		},
		[setRenderers]
	);

	const unregisterRenderer = useCallback(
		(type: string) => {
			setRenderers(current => {
				const updated = new Map(current);
				updated.delete(type);
				return updated;
			});
		},
		[setRenderers]
	);

	const getRenderer = useCallback((type: string) => globalRenderers.get(type) || renderers.get(type), [renderers, globalRenderers]);

	return useMemo(
		() => ({
			addOverlay,
			removeOverlay,
			getOverlaysForPage,
			registerRenderer,
			unregisterRenderer,
			getRenderer,
		}),
		[addOverlay, removeOverlay, getOverlaysForPage, registerRenderer, unregisterRenderer, getRenderer]
	);
};

export const useRenderOverlays = (overlayManager: OverlayManager) => {
	const renderOverlaysForPage = useCallback(
		async (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => {
			for (const overlay of overlayManager.getOverlaysForPage(page)) {
				const renderer = overlayManager.getRenderer(overlay.type);
				if (renderer) {
					try {
						await renderer.render(context, overlay, scale, overlayData?.[overlay.type]);
					} catch (error) {
						console.error(`Erro ao renderizar overlay ${overlay.id} (${overlay.type}):`, error);
					}
				} else {
					console.warn(`Nenhum renderizador encontrado para tipo: ${overlay.type}`);
				}
			}
		},
		[overlayManager]
	);

	return useMemo(() => ({ renderOverlaysForPage }), [renderOverlaysForPage]);
};
