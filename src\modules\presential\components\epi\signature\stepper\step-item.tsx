import { cn } from "@/shared/lib/utils";
import { Check } from "lucide-react";
import React from "react";
import { IStepConfig } from "./stepper-header";

interface IStepItemProps {
	step: IStepConfig;
	status: "active" | "completed" | "pending";
	isLast: boolean;
	isClickable: boolean;
	currentStep: number;
	variant: "desktop" | "mobile";
	onStepClick: (stepId: number) => void;
}

export const StepItem = ({ step, status, isLast, isClickable, currentStep, variant, onStepClick }: IStepItemProps) => {
	if (variant === "desktop") {
		return (
			<React.Fragment key={step.id}>
				<div
					className={cn(
						"flex flex-col items-center transition-all duration-200 flex-1 min-w-0",
						isClickable ? "cursor-pointer hover:scale-105" : "cursor-not-allowed"
					)}
					onClick={() => isClickable && onStepClick(step.id)}
				>
					<div
						className={cn(
							"w-10 h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 rounded-lg flex items-center justify-center border-2 transition-all duration-200 shadow-lg",
							status === "completed"
								? "bg-pormade border-pormade text-white"
								: status === "active"
								? "bg-white border-pormade text-pormade ring-2 md:ring-4 ring-pormade/20 z-10"
								: "bg-gray-100 border-gray-300 text-gray-400"
						)}
					>
						{status === "completed" ? (
							<Check className="w-4 h-4 md:w-5 md:h-5 lg:w-6 lg:h-6" />
						) : (
							<step.icon className="w-4 h-4 md:w-5 md:h-5 lg:w-6 lg:h-6" />
						)}
					</div>
					<div className="mt-2 md:mt-3 flex flex-col items-center justify-center text-center w-full px-1">
						<p
							className={cn(
								"text-xs md:text-xs lg:text-base font-medium transition-colors leading-tight",
								status === "active" || status === "completed" ? "text-gray-900" : "text-gray-500"
							)}
						>
							{step.title}
						</p>
						<p
							className={cn(
								"text-xs md:text-xs mt-0.5 md:mt-1 transition-colors leading-tight",
								status === "active" || status === "completed" ? "text-gray-600" : "text-gray-400"
							)}
						>
							{step.description}
						</p>
					</div>
				</div>
				{!isLast && (
					<div className="flex-1 flex items-center max-w-[40px] md:max-w-[60px] lg:max-w-[80px] mx-1 md:mx-2">
						<div
							className={cn(
								"h-[1px] md:h-[2px] rounded-full transition-all duration-300 w-full",
								currentStep > step.id ? "bg-pormade" : "bg-gray-300"
							)}
						/>
					</div>
				)}
			</React.Fragment>
		);
	}

	// Mobile variant
	return (
		<div key={step.id} className="flex items-center flex-1 min-w-0">
			<div
				className={cn(
					"relative flex flex-col items-center transition-all duration-200 w-full",
					isClickable && "cursor-pointer hover:scale-105"
				)}
				onClick={() => isClickable && onStepClick(step.id)}
			>
				<div
					className={cn(
						"w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center border-2 transition-all duration-200 shadow-sm",
						status === "completed"
							? "bg-pormade border-pormade text-white"
							: status === "active"
							? "bg-pormade border-pormade text-white ring-2 ring-pormade/30"
							: "bg-gray-100 border-gray-300 text-gray-400"
					)}
				>
					{status === "completed" ? <Check className="w-3.5 h-3.5 sm:w-4 sm:h-4" /> : <step.icon className="w-3.5 h-3.5 sm:w-4 sm:h-4" />}
				</div>
				<span
					className={cn(
						"text-xs sm:text-sm mt-1 sm:mt-1.5 text-center transition-colors leading-tight px-1",
						status === "active" || status === "completed" ? "text-gray-900 font-medium" : "text-gray-500"
					)}
				>
					{step.mobileTitle || step.title}
				</span>
			</div>
			{!isLast && (
				<div className="flex-1 h-[1px] mx-1 sm:mx-2 bg-gray-200 min-w-[8px] sm:min-w-[12px]">
					<div
						className={cn("h-full transition-all duration-300 rounded-full", currentStep > step.id ? "bg-pormade" : "bg-transparent")}
						style={{ width: currentStep > step.id ? "100%" : "0%" }}
					/>
				</div>
			)}
		</div>
	);
};
