import { IValidDocument } from "@/modules/signature/types/document/ validate-document.type";
import { maskSensitiveData } from "@/shared/lib/mask/sensitive-data";
import { apiInstance } from "@/shared/services/config/api";
import FormData from "form-data";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
	try {
		const { file } = await extractAndValidateFile(req);
		const buffer = await convertFileToBuffer(file);
		const formData = prepareFormData(buffer, file);

		const apiResponse = await apiInstance.post<IValidDocument>("/documentoAssinado/validar", formData, { headers: { ...formData.getHeaders() } });

		const safeData = maskSignatoriesData(apiResponse.data);
		return NextResponse.json(safeData);
	} catch (error: unknown) {
		let errorMessage = "Erro interno.";
		if (error instanceof Error) {
			errorMessage = error.message;
		}
		return NextResponse.json({ error: errorMessage }, { status: 500 });
	}
}

async function extractAndValidateFile(req: Request): Promise<{ file: File }> {
	const formData = await req.formData();
	const file = formData.get("documento") as File | null;

	if (!file) {
		throw new Error("Nenhum arquivo enviado.");
	}

	return { file };
}

async function convertFileToBuffer(file: File): Promise<Buffer> {
	const arrayBuffer = await file.arrayBuffer();
	return Buffer.from(arrayBuffer);
}

function prepareFormData(buffer: Buffer, file: File): FormData {
	const data = new FormData();
	data.append("documento", buffer, file.name);
	return data;
}

function maskSignatoriesData(data: IValidDocument): IValidDocument {
	const maskedSignatories = data.signatoriesData.map(signatory => ({
		...signatory,
		cpf_cnpj: maskSensitiveData(signatory.cpf_cnpj),
		email: maskSensitiveData(signatory.email),
		phone: maskSensitiveData(signatory.phone),
		representative: signatory.representative ? { ...signatory.representative, cpf: maskSensitiveData(signatory.representative.cpf) } : undefined,
	}));

	return { ...data, signatoriesData: maskedSignatories };
}
