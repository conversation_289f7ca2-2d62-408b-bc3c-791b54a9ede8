"use client";
import { IDocumentValidationService } from "@/modules/signature/services/manage/manage-validation-document";
import { IValidDocument } from "@/modules/signature/types/document/ validate-document.type";
import { useMutation } from "@tanstack/react-query";
import { useState } from "react";
import toast from "react-hot-toast";

export const useValidateDocument = (validationService: IDocumentValidationService) => {
	const [isValidDocument, setIsValidDocument] = useState<IValidDocument | null>(null);

	const { mutate: validateMutation, isPending } = useMutation({
		mutationFn: async (file: File) => {
			return await validationService.validate(file);
		},
		onSuccess: data => {
			toast.dismiss();
			toast.success("Documento verificado com sucesso!");
			setIsValidDocument(data);
		},
		onError: (error: unknown) => {
			toast.dismiss();
			if (error instanceof Error) {
				toast.error(error.message);
			} else {
				toast.error("Ocorreu um erro ao verificar o documento");
			}
		},
	});

	const handleUpload = (file: File | null) => {
		if (file) {
			toast.dismiss();
			toast.loading("Verificando documento...");
			validateMutation(file);
		}
	};

	return {
		isValidDocument,
		handleUpload,
		setIsValidDocument,
		isPending,
	};
};
