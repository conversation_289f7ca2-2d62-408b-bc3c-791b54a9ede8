import Pdf<PERSON>iewer from "@/modules/pdf/components/pdf";
import { usePdfActions } from "@/modules/pdf/hooks/actions/pdf-acitions.hook";
import { useLoadPdfProxy } from "@/modules/pdf/hooks/render/pdf-load.hook";
import { useDocumentModal } from "@/modules/signature/hooks/document/document-modal.hook";
import { IDocumentAlreadySigned } from "@/modules/signature/types/document/get-document.type";
import { Button } from "@/shared/components/ui/button";
import { Separator } from "@/shared/components/ui/separator";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { CheckCircle, CircleX, Download, Eye } from "lucide-react";
import React, { useState } from "react";
import { TitleCard } from "../../../cards/card-title";
import { SubTitleCard } from "../../../cards/subtitle";

export const DocumentSigned = ({ data }: { data: IDocumentAlreadySigned }) => {
	const [isDownloading, setIsDownloading] = useState(false);
	const DEBOUNCE_INTERVAL = 2000;

	useLoadPdfProxy({
		id: "not-signed-page",
		buffer: data.documentBuffer?.data ?? new ArrayBuffer(0),
	});
	const { downloadPdf } = usePdfActions({ fileName: "documento.pdf" });
	const { isOpen, setIsOpen } = useDocumentModal();

	const handleOverlayClick = () => {
		setIsOpen(false);
	};

	const stopPropagation = (e: React.MouseEvent) => {
		e.stopPropagation();
	};

	const handleDownload = () => {
		if (isDownloading) return;
		setIsDownloading(true);
		downloadPdf();
		setTimeout(() => {
			setIsDownloading(false);
		}, DEBOUNCE_INTERVAL);
	};

	return (
		<main id="signed-page" className="h-full w-full flex items-center justify-center">
			<div className="bg-white p-4 md:p-8 rounded-lg flex flex-col shadow-lg max-w-md sm:max-w-lg mx-auto w-full">
				<div className="flex items-center justify-center mb-6">
					<CheckCircle className="text-green-500 w-16 h-16 md:w-20 md:h-20" />
				</div>
				<TitleCard title={data.message} />
				<SubTitleCard title="Os detalhes da assinatura estão listados abaixo." />
				<div>
					<p>
						Data e hora da assinatura: <strong>{data.signDate ? new Date(data.signDate).toLocaleString() : ""}</strong>
					</p>
					<p>
						Assinado por: <strong>{data.signatoryName}</strong>
					</p>
					<Separator orientation="horizontal" className="my-5" />
					<div className="flex w-full flex-row gap-3 justify-center">
						<Button
							disabled={!data?.isDocumentAvaible || isDownloading}
							onClick={handleDownload}
							className="w-full sm:w-1/2 gap-5 text-white bg-gray-800 hover:bg-gray-900 h-[50px]"
						>
							<Download />
							Baixar
						</Button>
						<Button
							disabled={!data?.isDocumentAvaible}
							onClick={() => setIsOpen(true)}
							className="w-full sm:w-1/2 gap-5 text-white bg-gray-800 hover:bg-gray-900 h-[50px]"
						>
							<Eye />
							Visualizar
						</Button>
					</div>
					<p className="text-xs text-gray-500 mt-4 text-center">
						* Você só poderá baixar ou visualizar o documento quando todos assinarem.
					</p>
				</div>
			</div>

			{isOpen && (
				<div onClick={handleOverlayClick} className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
					<div
						onClick={stopPropagation}
						className="w-[800px] sm:relative mx-1 sm:m-3 h-[96vh] bg-white overflow-hidden flex flex-col rounded-[10px] md:rounded-lg shadow-lg"
					>
						<header className="w-full flex justify-between items-center p-4 border-b">
							<h1 className="text-2xl font-bold text-gray-800">Documento</h1>
							<button className="text-3xl text-gray-600 hover:text-gray-800" onClick={() => setIsOpen(false)}>
								<CircleX />
							</button>
						</header>

						<div className="flex-1 overflow-auto">
							{data?.documentBuffer?.data ? (
								<PdfViewer id="not-signed-page" buffer={data.documentBuffer.data} isModal />
							) : (
								<div className="w-full h-full flex items-center justify-center">
									<Skeleton className="w-full h-full" />
								</div>
							)}
						</div>
					</div>
				</div>
			)}
		</main>
	);
};
