"use client";

import { useValidationSingature } from "@/modules/signature/hooks/signature/validate-signature/validate-signature-form.hook";
import { useSignatureValidation } from "@/modules/signature/hooks/signature/validate-signature/valide-signature-input.hook";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { TicketCheck } from "lucide-react";
import { TitleCard } from "../../cards/card-title";
import { SubTitleCard } from "../../cards/subtitle";

export const ValidateSignatureForm = () => {
	const { register, handleSubmit, formState } = useValidationSingature();
	const { onSubmit, isHashEmpty } = useSignatureValidation();

	return (
		<form onSubmit={handleSubmit(data => onSubmit(data.hash.trim()))}>
			<header className="text-center w-full mb-2">
				<TitleCard title="Verificar Assinatura" />
				<SubTitleCard title="Para verificar a validade de sua assinatura, você pode escanear o QRCode no email ou inserir o código manualmente." />
			</header>

			<div className="mb-8 w-full">
				<Input
					{...register("hash", { required: true })}
					placeholder="Digite o código da assinatura aqui"
					className="w-full h-[60px] px-4 py-3 text-lg border border-gray-300 rounded-lg"
				/>
				{formState.errors.hash && <span className="text-red-500">{String(formState.errors.hash?.message)}</span>}
			</div>

			<Button
				type="submit"
				className="w-full h-[60px] font-bold text-white bg-gray-800 hover:bg-gray-900 flex items-center justify-center gap-2 py-3"
				disabled={isHashEmpty(formState.dirtyFields.hash ? formState.dirtyFields.hash.toString().trim() : "")}
			>
				<TicketCheck />
				Verificar Assinatura
			</Button>

			<p className="text-xs text-gray-500 mt-4 text-center">
				* Apenas assinaturas geradas dentro do nosso sistema são válidas para verificação.
			</p>
		</form>
	);
};
