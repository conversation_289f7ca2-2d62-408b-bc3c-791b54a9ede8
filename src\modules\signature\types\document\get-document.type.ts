export interface IGetDocument {
	idDocument: number;
	idSignature: number;
	signatoryName: string;
	rubric: IRubric;
	documentBuffer: DataBuffer | null;
}

interface IRubric {
	pageIndex: number;
	coordinates: {
		x: number;
		y: number;
	};
}

export interface DataBuffer {
	type: string;
	data: ArrayBuffer;
}

export interface IDocumentAlreadySigned {
	message: string;
	signatoryName: string;
	signDate: string;
	isDocumentAvaible: boolean;
	documentBuffer: DataBuffer | null;
}

export interface IDocumentData<TData> {
	isDocumentSigned: boolean | undefined;
	data: TData;
}
