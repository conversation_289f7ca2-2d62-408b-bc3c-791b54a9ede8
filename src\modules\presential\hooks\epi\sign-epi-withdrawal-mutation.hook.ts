import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { signEpiWithDrawalRequest, ISignWithDrawalParams } from "../../services/requests/epi/sign-epi-withdrawal";

export const useSignEpiWithdrawalMutation = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationKey: ["epi", "withdrawal", "sign"],
		mutationFn: async (params: ISignWithDrawalParams) => {
			const res = await signEpiWithDrawalRequest(params);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["epi", "return", "all"] });
			toast.dismiss();
			toast.success("Retirada de EPI assinada com sucesso!");
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});
};
