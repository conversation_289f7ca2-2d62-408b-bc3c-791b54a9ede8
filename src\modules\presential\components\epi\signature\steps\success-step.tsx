import { But<PERSON> } from "@/shared/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/shared/components/ui/card";
import { CheckCircle, Plus, Copy } from "lucide-react";
import { useAtomValue } from "jotai";
import { signatureDataAtom } from "@/modules/presential/atoms/epi-signature.atoms";

interface ISuccessStepProps {
	onReturnToProvider: (keepEpis?: boolean) => void;
}

export const SuccessStep = ({ onReturnToProvider }: ISuccessStepProps) => {
	const signatureData = useAtomValue(signatureDataAtom);

	const handleReturnWithSameEpis = () => onReturnToProvider(true);
	const handleReturnWithNewEpis = () => onReturnToProvider(false);

	return (
		<div className="space-y-6">
			<div className="text-center">
				<div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
					<CheckCircle className="w-12 h-12 text-green-600" />
				</div>
				<h2 className="text-2xl font-bold text-gray-900 mb-2">Assinatura Concluída!</h2>
				<p className="text-gray-600">O processo de assinatura dos EPIs foi concluído com sucesso.</p>
			</div>

			<Card className="border-2 border-green-200 bg-green-50">
				<CardHeader className="pb-4">
					<CardTitle className="flex items-center gap-2 text-green-700">
						<CheckCircle className="w-5 h-5" />
						Resumo da Assinatura
					</CardTitle>
					<CardDescription className="text-green-600">Detalhes do processo concluído</CardDescription>
				</CardHeader>
				<CardContent className="space-y-3">
					<div className="flex justify-between items-center">
						<span className="text-gray-700">Assinante:</span>
						<span className="font-medium text-gray-900">{signatureData.personName}</span>
					</div>
					<div className="flex justify-between items-center">
						<span className="text-gray-700">CPF:</span>
						<span className="font-medium text-gray-900">{signatureData.personCpf}</span>
					</div>
					<div className="flex justify-between items-center">
						<span className="text-gray-700">EPIs assinados:</span>
						<span className="font-medium text-gray-900">{((signatureData.selectedEpis as any[]) || []).length} itens</span>
					</div>
					<div className="flex justify-between items-center">
						<span className="text-gray-700">Data e hora:</span>
						<span className="font-medium text-gray-900">{new Date().toLocaleString("pt-BR")}</span>
					</div>
				</CardContent>
			</Card>
			<div className="space-y-4">
				<h3 className="text-lg font-semibold text-gray-900 text-center">O que você gostaria de fazer agora?</h3>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<Card className="cursor-pointer hover:shadow-lg transition-all duration-200 border-2 hover:border-pormade/50">
						<CardContent className="p-6">
							<Button
								onClick={handleReturnWithSameEpis}
								variant="outline"
								className="w-full h-auto flex-col gap-3 p-6 border-none hover:bg-pormade/5"
							>
								<div className="w-12 h-12 bg-pormade/10 rounded-full flex items-center justify-center">
									<Copy className="w-6 h-6 text-pormade" />
								</div>
								<div className="text-center">
									<div className="font-semibold text-gray-900 mb-1">Usar os Mesmos EPIs</div>
									<div className="text-sm text-gray-600">Continuar com a mesma seleção de EPIs para outro assinante</div>
								</div>
							</Button>
						</CardContent>
					</Card>
					<Card className="cursor-pointer hover:shadow-lg transition-all duration-200 border-2 hover:border-blue-500/50">
						<CardContent className="p-6">
							<Button
								onClick={handleReturnWithNewEpis}
								variant="outline"
								className="w-full h-auto flex-col gap-3 p-6 border-none hover:bg-blue-50"
							>
								<div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
									<Plus className="w-6 h-6 text-blue-600" />
								</div>
								<div className="text-center">
									<div className="font-semibold text-gray-900 mb-1">Selecionar Novos EPIs</div>
									<div className="text-sm text-gray-600">Escolher uma nova seleção de EPIs para assinatura</div>
								</div>
							</Button>
						</CardContent>
					</Card>
				</div>
			</div>
			<div className="text-center text-sm text-gray-500">
				<p>Selecione uma opção para continuar com o próximo processo de assinatura.</p>
			</div>
		</div>
	);
};
