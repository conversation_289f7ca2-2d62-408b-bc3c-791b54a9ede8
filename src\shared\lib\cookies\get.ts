"use server";

import { ApiResponseReturn, IGlobalMessage } from "@/shared/types/requests";
import { cookies } from "next/headers";

export const getCookie = async (name: string): Promise<ApiResponseReturn<IGlobalMessage & { value?: string }>> => {
	try {
		const cookieStore = await cookies();
		const cookie = cookieStore.get(name);

		if (!cookie) {
			return {
				success: false,
				data: {
					message: "Cookie não encontrado",
				},
				status: 404,
			};
		}

		return {
			success: true,
			data: {
				message: cookie.value,
				value: cookie.value,
			},
			status: 200,
		};
	} catch (error) {
		return {
			success: false,
			data: {
				message: `Erro ao buscar cookie: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
			},
			status: 500,
		};
	}
};
