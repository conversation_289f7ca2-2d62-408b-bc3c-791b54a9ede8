import { But<PERSON> } from "@/shared/components/ui/button";
import { PlusCircle, Tag } from "lucide-react";

interface ActionButtonsProps {
	onCreateTerm: () => void;
	onCreateTag: () => void;
}

export const ActionButtons = ({ onCreateTerm, onCreateTag }: ActionButtonsProps) => {
	return (
		<div className="flex w-full lg:justify-end  gap-2">
			<Button variant="outline" className="flex rounded-lg w-1/2 lg:w-auto items-center gap-2" onClick={onCreateTag}>
				<Tag size={16} /> Nova Tag
			</Button>
			<Button
				className="flex rounded-lg bg-gradient-to-br  w-1/2  lg:w-auto from-gray-800 to-gray-600 items-center gap-2"
				onClick={onCreateTerm}
			>
				<PlusCircle size={16} /> Novo Termo
			</Button>
		</div>
	);
};
